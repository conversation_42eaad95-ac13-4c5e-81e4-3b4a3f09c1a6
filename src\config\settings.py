"""
智能日志分析器配置管理

使用pydantic BaseSettings管理所有配置项，支持环境变量和配置文件
"""

from pydantic import Field, validator
try:
    from pydantic_settings import BaseSettings
except ImportError:
    # 兼容老版本的pydantic
    from pydantic import BaseSettings
from typing import List, Optional, Dict, Any
import os
from pathlib import Path

class DeepSeekConfig(BaseSettings):
    """DeepSeek模型配置"""
    
    # 主要对话模型 (DeepSeek-V3-0324)
    api_base: str = Field(..., env="DEEPSEEK_API_BASE", description="DeepSeek API基础URL")
    api_key: str = Field(..., env="DEEPSEEK_API_KEY", description="DeepSeek API密钥") 
    model: str = Field("openai/DeepSeek-V3-0324", env="DEEPSEEK_MODEL", description="DeepSeek模型名称")
    temperature: float = Field(0.6, env="DEEPSEEK_TEMPERATURE", description="生成温度")
    max_tokens: int = Field(4096, env="DEEPSEEK_MAX_TOKENS", description="最大令牌数")
    timeout: int = Field(60, env="DEEPSEEK_TIMEOUT", description="请求超时时间(秒)")
    max_retries: int = Field(3, env="DEEPSEEK_MAX_RETRIES", description="最大重试次数")
    
    # 推理模型 (DeepSeek-R1，可选)
    r1_api_base: str = Field("", env="DEEPSEEK_R1_API_BASE", description="DeepSeek-R1 API基础URL")
    r1_api_key: str = Field("", env="DEEPSEEK_R1_API_KEY", description="DeepSeek-R1 API密钥") 
    r1_model: str = Field("openai/DeepSeek-R1", env="DEEPSEEK_R1_MODEL", description="DeepSeek-R1模型名称")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
        extra = 'ignore'  # 忽略额外的环境变量
    
    @validator('temperature')
    def validate_temperature(cls, v):
        if not 0.0 <= v <= 2.0:
            raise ValueError('temperature必须在0.0-2.0之间')
        return v
    
    @validator('max_tokens')
    def validate_max_tokens(cls, v):
        if v <= 0:
            raise ValueError('max_tokens必须大于0')
        return v

class RAGFlowConfig(BaseSettings):
    """RAGFlow知识库配置"""
    
    base_url: str = Field(..., env="RAGFLOW_BASE_URL", description="RAGFlow API基础URL")
    api_key: str = Field(..., env="RAGFLOW_API_KEY", description="RAGFlow API密钥")
    knowledge_base_id: str = Field(..., env="RAGFLOW_KNOWLEDGE_BASE_ID", description="知识库ID")
    timeout: int = Field(60, env="RAGFLOW_TIMEOUT", description="请求超时时间(秒)")
    max_retries: int = Field(3, env="RAGFLOW_MAX_RETRIES", description="最大重试次数")
    assistant_name: str = Field("OAuth2分析专家助手", env="RAGFLOW_ASSISTANT_NAME", description="助手名称")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
        extra = 'ignore'  # 忽略额外的环境变量
    
    @validator('knowledge_base_id')
    def validate_knowledge_base_id(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('knowledge_base_id不能为空')
        return v.strip()

class SearchConfig(BaseSettings):
    """搜索引擎配置"""
    
    ripgrep_path: str = Field("rg", env="RIPGREP_PATH", description="ripgrep可执行文件路径")
    max_results: int = Field(100, env="SEARCH_MAX_RESULTS", description="最大搜索结果数")
    max_context_lines: int = Field(10, env="SEARCH_CONTEXT_LINES", description="上下文行数")
    max_file_size_mb: int = Field(100, env="MAX_FILE_SIZE_MB", description="最大文件大小(MB)")
    parallel_searches: int = Field(4, env="PARALLEL_SEARCHES", description="并行搜索数")
    cache_enabled: bool = Field(True, env="SEARCH_CACHE_ENABLED", description="是否启用搜索缓存")
    cache_ttl_minutes: int = Field(60, env="SEARCH_CACHE_TTL", description="缓存过期时间(分钟)")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
        extra = 'ignore'  # 忽略额外的环境变量
    
    @validator('max_results')
    def validate_max_results(cls, v):
        if v <= 0:
            raise ValueError('max_results必须大于0')
        return min(v, 10000)  # 限制最大值
    
    @validator('max_context_lines')
    def validate_max_context_lines(cls, v):
        if v < 0:
            raise ValueError('max_context_lines不能为负数')
        return min(v, 100)  # 限制最大值

class DatabaseConfig(BaseSettings):
    """数据库配置（可选，用于持久化）"""
    
    enabled: bool = Field(False, env="DB_ENABLED", description="是否启用数据库")
    url: str = Field("sqlite:///log_analyzer.db", env="DATABASE_URL", description="数据库连接URL")
    pool_size: int = Field(5, env="DB_POOL_SIZE", description="连接池大小")
    echo: bool = Field(False, env="DB_ECHO", description="是否打印SQL语句")

class LoggingConfig(BaseSettings):
    """日志配置"""
    
    level: str = Field("INFO", env="LOG_LEVEL", description="日志级别")
    format: str = Field(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        env="LOG_FORMAT",
        description="日志格式"
    )
    file_enabled: bool = Field(True, env="LOG_FILE_ENABLED", description="是否输出到文件")
    file_path: str = Field("logs/app.log", env="LOG_FILE_PATH", description="日志文件路径")
    max_file_size_mb: int = Field(50, env="LOG_MAX_FILE_SIZE_MB", description="日志文件最大大小(MB)")
    backup_count: int = Field(5, env="LOG_BACKUP_COUNT", description="日志文件备份数量")
    
    @validator('level')
    def validate_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'日志级别必须是: {valid_levels}')
        return v.upper()

class PerformanceConfig(BaseSettings):
    """性能配置"""
    
    max_concurrent_analyses: int = Field(5, env="MAX_CONCURRENT_ANALYSES", description="最大并发分析数")
    analysis_timeout_minutes: int = Field(30, env="ANALYSIS_TIMEOUT_MINUTES", description="分析超时时间(分钟)")
    memory_limit_gb: float = Field(4.0, env="MEMORY_LIMIT_GB", description="内存限制(GB)")
    disk_cache_size_gb: float = Field(1.0, env="DISK_CACHE_SIZE_GB", description="磁盘缓存大小(GB)")
    
    @validator('max_concurrent_analyses')
    def validate_max_concurrent_analyses(cls, v):
        if v <= 0:
            raise ValueError('max_concurrent_analyses必须大于0')
        return min(v, 50)  # 限制最大值

class SecurityConfig(BaseSettings):
    """安全配置"""
    
    api_key_required: bool = Field(True, env="API_KEY_REQUIRED", description="是否需要API密钥")
    api_keys: List[str] = Field(default_factory=list, env="API_KEYS", description="有效的API密钥列表")
    rate_limit_per_minute: int = Field(60, env="RATE_LIMIT_PER_MINUTE", description="每分钟请求限制")
    allowed_log_paths: List[str] = Field(
        default_factory=lambda: ["/tmp", "/var/log", "./data"],
        env="ALLOWED_LOG_PATHS",
        description="允许访问的日志路径"
    )
    
    @validator('api_keys', pre=True)
    def parse_api_keys(cls, v):
        if isinstance(v, str):
            return [key.strip() for key in v.split(',') if key.strip()]
        return v
    
    @validator('allowed_log_paths', pre=True) 
    def parse_allowed_paths(cls, v):
        if isinstance(v, str):
            return [path.strip() for path in v.split(',') if path.strip()]
        return v

class AppConfig(BaseSettings):
    """应用主配置"""
    
    # 子配置
    deepseek: DeepSeekConfig = Field(default_factory=DeepSeekConfig)
    ragflow: RAGFlowConfig = Field(default_factory=RAGFlowConfig)
    search: SearchConfig = Field(default_factory=SearchConfig)
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    performance: PerformanceConfig = Field(default_factory=PerformanceConfig)
    security: SecurityConfig = Field(default_factory=SecurityConfig)
    
    # 应用基本配置
    app_name: str = Field("intelligent-log-analyzer", env="APP_NAME", description="应用名称")
    version: str = Field("1.0.0", env="APP_VERSION", description="应用版本")
    debug: bool = Field(False, env="DEBUG", description="调试模式")
    data_dir: str = Field("./data", env="DATA_DIR", description="数据目录")
    temp_dir: str = Field("./temp", env="TEMP_DIR", description="临时目录")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        extra = 'ignore'  # 忽略额外的环境变量
        
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 创建必要的目录
        self._create_directories()
    
    def _create_directories(self):
        """创建必要的目录"""
        directories = [
            self.data_dir,
            self.temp_dir,
            os.path.dirname(self.logging.file_path),
            f"{self.data_dir}/logs",
            f"{self.data_dir}/reports",
            f"{self.data_dir}/knowledge_base"
        ]
        
        for dir_path in directories:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    def validate_config(self) -> List[str]:
        """验证配置并返回警告列表"""
        warnings = []
        
        # 检查ripgrep是否可用
        import shutil
        if not shutil.which(self.search.ripgrep_path):
            warnings.append(f"ripgrep未找到: {self.search.ripgrep_path}")
        
        # 检查数据目录权限
        data_path = Path(self.data_dir)
        if not data_path.exists() or not os.access(data_path, os.W_OK):
            warnings.append(f"数据目录不可写: {self.data_dir}")
        
        # 检查内存限制
        if self.performance.memory_limit_gb < 1.0:
            warnings.append("内存限制可能过小，建议至少1GB")
        
        return warnings
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'app_name': self.app_name,
            'version': self.version,
            'debug': self.debug,
            'deepseek': {
                'model': self.deepseek.model,
                'temperature': self.deepseek.temperature,
                'max_tokens': self.deepseek.max_tokens
            },
            'search': {
                'max_results': self.search.max_results,
                'max_context_lines': self.search.max_context_lines,
                'parallel_searches': self.search.parallel_searches
            },
            'performance': {
                'max_concurrent_analyses': self.performance.max_concurrent_analyses,
                'memory_limit_gb': self.performance.memory_limit_gb
            }
        }

# 全局配置实例
_config_instance: Optional[AppConfig] = None

def get_config() -> AppConfig:
    """获取全局配置实例"""
    global _config_instance
    if _config_instance is None:
        _config_instance = AppConfig()
    return _config_instance

def reload_config() -> AppConfig:
    """重新加载配置"""
    global _config_instance
    _config_instance = AppConfig()
    return _config_instance

def load_config_from_file(config_file: str) -> AppConfig:
    """从指定文件加载配置"""
    global _config_instance
    
    if not os.path.exists(config_file):
        raise FileNotFoundError(f"配置文件不存在: {config_file}")
    
    # 临时设置环境变量
    original_env_file = os.environ.get('ENV_FILE')
    os.environ['ENV_FILE'] = config_file
    
    try:
        _config_instance = AppConfig()
        return _config_instance
    finally:
        # 恢复原始环境变量
        if original_env_file:
            os.environ['ENV_FILE'] = original_env_file
        elif 'ENV_FILE' in os.environ:
            del os.environ['ENV_FILE'] 