"""
DeepSeek兼容的LLM实现

基于log-agent/deepseek_compatible_litellm.py优化
支持BYD内部服务和外部API的双重配置
"""

import os
import logging
from typing import Any, Dict, Optional, AsyncGenerator
from google.adk.models.lite_llm import LiteLlm
from google.adk.models.llm_request import LlmRequest
from google.adk.models.llm_response import LlmResponse

from config.simple_config import get_config
from core.exceptions import DeepSeekError

logger = logging.getLogger(__name__)


class DeepSeekLiteLlm(LiteLlm):
    """
    DeepSeek兼容性LiteLLM实现
    
    关键修复：
    1. role="developer" → role="system" (解决工具调用问题)
    2. 确保function calling正常工作
    3. 保持ADK标准接口
    """
    
    def __init__(self, 
                 model: str = "openai/DeepSeek-V3-0324",
                 api_key: Optional[str] = None,
                 base_url: Optional[str] = None,
                 temperature: float = 0.6,
                 max_tokens: int = 4096,
                 timeout: float = 60.0,
                 use_r1_model: bool = False,
                 **kwargs):
        """初始化DeepSeek兼容的LiteLLM实例"""
        
        # 获取配置
        config = get_config()
        deepseek_config = config.deepseek
        
        # 🎯 支持BYD内部DeepSeek服务配置
        if not api_key or not base_url:
            if use_r1_model and deepseek_config.r1_api_key:
                # 使用DeepSeek-R1推理模型
                api_key = deepseek_config.r1_api_key
                base_url = deepseek_config.r1_api_base
                model = deepseek_config.r1_model
                logger.info("使用DeepSeek-R1推理模型")
            else:
                # 使用主要的DeepSeek-V3-0324模型
                api_key = deepseek_config.api_key
                base_url = deepseek_config.api_base
                model = deepseek_config.model
                logger.info("使用DeepSeek-V3-0324对话模型")
        
        # 从配置中获取其他参数
        temperature = temperature or deepseek_config.temperature
        max_tokens = max_tokens or deepseek_config.max_tokens
        timeout = timeout or deepseek_config.timeout
        
        # 初始化基础LiteLLM，但使用修复后的参数
        super().__init__(
            model=model,
            api_key=api_key,
            base_url=base_url,
            temperature=temperature,
            max_tokens=max_tokens,
            timeout=timeout,
            **kwargs
        )
        
        logger.info(f"DeepSeek兼容性LiteLLM初始化完成")
        logger.info(f"   - Model: {model}")
        logger.info(f"   - Base URL: {base_url}")
        logger.info(f"   - Temperature: {temperature}")
        logger.info(f"   - Max Tokens: {max_tokens}")
    
    def _fix_developer_role_in_messages(self, messages: list) -> list:
        """
        修复ADK的role="developer"问题
        
        根据DeepSeek API文档，支持的角色：
        - system, user, assistant, tool
        - 不支持 developer 角色
        
        解决方案：将developer角色转换为system角色
        """
        fixed_messages = []
        
        for message in messages:
            # 检查是否是ChatCompletionDeveloperMessage或包含developer角色
            if hasattr(message, 'role') and message.role == 'developer':
                logger.debug(f"🔧 修复角色: developer → system")
                # 创建修复后的消息
                fixed_message = {
                    'role': 'system',
                    'content': message.content
                }
                fixed_messages.append(fixed_message)
            elif isinstance(message, dict) and message.get('role') == 'developer':
                logger.debug(f"🔧 修复字典角色: developer → system")
                fixed_message = message.copy()
                fixed_message['role'] = 'system'
                fixed_messages.append(fixed_message)
            else:
                # 保持其他消息不变
                if hasattr(message, '__dict__'):
                    # 对象转字典
                    fixed_messages.append({
                        'role': message.role,
                        'content': message.content
                    })
                else:
                    # 已经是字典
                    fixed_messages.append(message)
        
        return fixed_messages
    
    def _log_function_calling_debug(self, llm_request: LlmRequest, tools: list):
        """记录工具调用调试信息"""
        if tools:
            logger.info(f"工具调用调试:")
            logger.info(f"   - 工具数量: {len(tools)}")
            for i, tool in enumerate(tools[:3]):  # 只显示前3个
                tool_name = tool.get('function', {}).get('name', 'unknown')
                logger.info(f"   - 工具{i+1}: {tool_name}")
            
            # 检查是否有强制工具调用指令
            if llm_request.config and llm_request.config.system_instruction:
                instruction = llm_request.config.system_instruction
                if any(keyword in instruction.lower() for keyword in ['must call', 'tool', 'function']):
                    logger.info(f"   - 检测到强制工具调用指令")
                else:
                    logger.warning(f"   - 未检测到明确的工具调用指令")

    async def generate_content_async(
        self, llm_request: LlmRequest, stream: bool = False
    ) -> AsyncGenerator[LlmResponse, None]:
        """
        重写generate_content_async以修复DeepSeek兼容性问题
        """
        logger.debug(f"🚀 DeepSeek工具调用请求开始")
        
        # 在调用父类方法前，我们需要拦截并修复消息
        # 这里我们通过monkey patching来实现
        original_get_completion_inputs = None
        
        try:
            # 导入需要的模块
            from google.adk.models import lite_llm as litellm_module
            
            # 保存原始函数
            original_get_completion_inputs = litellm_module._get_completion_inputs
            
            # 创建修复版本的_get_completion_inputs
            def fixed_get_completion_inputs(request):
                # 获取原始函数的所有返回值（ADK 1.7.0返回4个值）
                original_result = original_get_completion_inputs(request)
                
                # 解包所有返回值
                if len(original_result) == 4:
                    messages, tools, response_format, extra = original_result
                elif len(original_result) == 3:
                    messages, tools, response_format = original_result
                    extra = None
                else:
                    # 兼容其他可能的版本
                    messages = original_result[0] if len(original_result) > 0 else []
                    tools = original_result[1] if len(original_result) > 1 else None
                    response_format = original_result[2] if len(original_result) > 2 else None
                    extra = original_result[3] if len(original_result) > 3 else None
                
                # 修复messages中的developer角色
                if messages:
                    messages = self._fix_developer_role_in_messages(list(messages))
                    logger.debug(f"消息角色修复完成，共{len(messages)}条消息")
                
                # 调试工具信息
                if tools:
                    self._log_function_calling_debug(request, tools)
                
                # 返回相同数量的值
                if len(original_result) == 4:
                    return messages, tools, response_format, extra
                elif len(original_result) == 3:
                    return messages, tools, response_format
                else:
                    return original_result  # 保持原始结构
            
            # 替换函数
            litellm_module._get_completion_inputs = fixed_get_completion_inputs
            
            # 调用父类方法
            async for response in super().generate_content_async(llm_request, stream):
                # 检查响应是否包含工具调用
                if hasattr(response, 'content') and response.content:
                    # 这里可以添加响应调试日志
                    if hasattr(response.content, 'parts'):
                        for part in response.content.parts:
                            if hasattr(part, 'function_call') and part.function_call:
                                logger.info(f"✅ DeepSeek返回了工具调用: {part.function_call.name}")
                
                yield response
                
        finally:
            # 恢复原始函数
            if original_get_completion_inputs:
                litellm_module._get_completion_inputs = original_get_completion_inputs


class DeepSeekModelFactory:
    """DeepSeek模型工厂 - 创建兼容的模型实例"""
    
    @staticmethod
    def create_model(model_type: str = "v3024", config=None, **kwargs) -> DeepSeekLiteLlm:
        """
        创建DeepSeek兼容的模型实例
        
        Args:
            model_type: 模型类型 ("v3024" 或 "r1")
            config: 配置对象（SimpleDeepSeekConfig或类似对象）
            **kwargs: 其他参数
            
        Returns:
            配置好的DeepSeekLiteLlm实例
        """
        use_r1_model = model_type.lower() == "r1"
        
        logger.info(f"创建DeepSeek模型实例: {model_type}")
        
        # 如果提供了配置对象，从中提取参数
        if config:
            # 根据模型类型选择配置
            if use_r1_model:
                # 使用R1配置
                model_config = {
                    'model': getattr(config, 'r1_model', 'openai/DeepSeek-R1'),
                    'api_base': getattr(config, 'r1_api_base', ''),
                    'api_key': getattr(config, 'r1_api_key', ''),
                    'temperature': getattr(config, 'temperature', 0.1),
                    'max_tokens': getattr(config, 'max_tokens', 4096),
                    'timeout': getattr(config, 'timeout', 60),
                    'max_retries': getattr(config, 'max_retries', 3)
                }
            else:
                # 使用V3配置
                model_config = {
                    'model': getattr(config, 'model', 'openai/DeepSeek-V3-0324'),
                    'api_base': getattr(config, 'api_base', ''),
                    'api_key': getattr(config, 'api_key', ''),
                    'temperature': getattr(config, 'temperature', 0.6),
                    'max_tokens': getattr(config, 'max_tokens', 4096),
                    'timeout': getattr(config, 'timeout', 60),
                    'max_retries': getattr(config, 'max_retries', 3)
                }
            
            # 合并用户提供的额外参数
            model_config.update(kwargs)
            
            return DeepSeekLiteLlm(
                use_r1_model=use_r1_model,
                **model_config
            )
        else:
            # 没有配置时使用默认参数
            return DeepSeekLiteLlm(
                use_r1_model=use_r1_model,
                **kwargs
            )
    
    @staticmethod
    def create_v3024_model(config=None, **kwargs) -> DeepSeekLiteLlm:
        """创建DeepSeek-V3-0324对话模型"""
        return DeepSeekModelFactory.create_model("v3024", config, **kwargs)
    
    @staticmethod
    def create_r1_model(config=None, **kwargs) -> DeepSeekLiteLlm:
        """创建DeepSeek-R1推理模型"""
        return DeepSeekModelFactory.create_model("r1", config, **kwargs)
    
    @staticmethod 
    def test_function_calling():
        """测试DeepSeek function calling能力"""
        logger.info("🧪 开始测试DeepSeek function calling...")
        
        # 这里可以添加测试逻辑
        model = DeepSeekModelFactory.create_model()
        logger.info(f"✅ DeepSeek模型创建成功: {model.model}")
        
        return model


# 向后兼容
def create_deepseek_model(model_type: str = "v3024", **kwargs) -> DeepSeekLiteLlm:
    """向后兼容的模型创建函数"""
    return DeepSeekModelFactory.create_model(model_type, **kwargs)


def create_deepseek_v3024(**kwargs) -> DeepSeekLiteLlm:
    """创建DeepSeek-V3-0324模型"""
    return DeepSeekModelFactory.create_v3024_model(**kwargs)


def create_deepseek_r1(**kwargs) -> DeepSeekLiteLlm:
    """创建DeepSeek-R1模型"""
    return DeepSeekModelFactory.create_r1_model(**kwargs)


if __name__ == "__main__":
    # 测试脚本
    logging.basicConfig(level=logging.INFO)
    
    try:
        model = DeepSeekModelFactory.test_function_calling()
        print("✅ DeepSeek兼容性测试通过")
    except Exception as e:
        print(f"❌ DeepSeek兼容性测试失败: {e}")
        print("   请检查配置文件中的DeepSeek配置") 