"""
智能日志分析器异常体系 - MVP简化版

只包含基础异常类，避免过度设计
"""


class LogAnalyzerException(Exception):
    """智能日志分析器基础异常"""
    pass


class ConfigurationError(LogAnalyzerException):
    """配置相关错误"""
    pass


class DeepSeekError(LogAnalyzerException):
    """DeepSeek模型相关错误"""
    pass


class RAGFlowError(LogAnalyzerException):
    """RAGFlow服务相关错误"""
    pass


class LogSearchError(LogAnalyzerException):
    """日志搜索相关错误"""
    pass


class FileProcessingError(LogAnalyzerException):
    """文件处理相关错误"""
    pass


class AnalysisError(LogAnalyzerException):
    """分析引擎相关错误"""
    pass


class ReportGenerationError(LogAnalyzerException):
    """报告生成相关错误"""
    pass


class NetworkError(LogAnalyzerException):
    """网络连接相关错误"""
    pass


class ValidationError(LogAnalyzerException):
    """数据验证相关错误"""
    pass 