# 智能日志分析器环境变量配置示例
# 复制此文件为 .env 并填入实际配置值

# ================================
# DeepSeek 模型配置 (BYD内部服务)
# ================================
# DeepSeek-V3-0324 (主要对话模型)
DEEPSEEK_API_BASE=http://dev-ali.deepseek.byd.com/api/predict/byd_irc_deepseek_v0324/v1
DEEPSEEK_API_KEY=Yjk0YzAwNTE5ZWE5NWNkY2VjZTU3MzQxYTJlZjcxM2JlY2U0ODg4Zg==
DEEPSEEK_MODEL=openai/DeepSeek-V3-0324
DEEPSEEK_TEMPERATURE=0.6
DEEPSEEK_MAX_TOKENS=4096
DEEPSEEK_TIMEOUT=60
DEEPSEEK_MAX_RETRIES=3

# DeepSeek-R1 (推理模型，可选)
DEEPSEEK_R1_API_BASE=http://dev-ali.deepseek.byd.com/api/predict/byd_irc_deepseek_r1/v1
DEEPSEEK_R1_API_KEY=MDMxOWNmMzA2MmM3MDhhZTg3MGI0MzQxMTYwOWNiYTlmZDAzOWRjZA==
DEEPSEEK_R1_MODEL=openai/DeepSeek-R1

# ================================
# RAGFlow 知识库配置
# ================================
RAGFLOW_BASE_URL=http://************
RAGFLOW_API_KEY=ragflow-g3Y2U3NmJlNTEyMjExZjA4MjNiM2FhZW
RAGFLOW_KNOWLEDGE_BASE_ID=fb14784c615011f0bb00d23d43f130ad
RAGFLOW_ASSISTANT_NAME=OAuth2分析专家助手
RAGFLOW_TIMEOUT=60
RAGFLOW_MAX_RETRIES=3

# ================================
# 搜索引擎配置
# ================================
RIPGREP_PATH=rg
SEARCH_MAX_RESULTS=100
SEARCH_CONTEXT_LINES=10
MAX_FILE_SIZE_MB=100
PARALLEL_SEARCHES=4
SEARCH_CACHE_ENABLED=true
SEARCH_CACHE_TTL=60

# ================================
# 数据库配置（可选）
# ================================
DB_ENABLED=false
DATABASE_URL=sqlite:///log_analyzer.db
DB_POOL_SIZE=5
DB_ECHO=false

# ================================
# 日志配置
# ================================
LOG_LEVEL=INFO
LOG_FORMAT="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_FILE_ENABLED=true
LOG_FILE_PATH=logs/app.log
LOG_MAX_FILE_SIZE_MB=50
LOG_BACKUP_COUNT=5

# ================================
# 性能配置
# ================================
MAX_CONCURRENT_ANALYSES=5
ANALYSIS_TIMEOUT_MINUTES=30
MEMORY_LIMIT_GB=4.0
DISK_CACHE_SIZE_GB=1.0

# ================================
# 安全配置
# ================================
API_KEY_REQUIRED=true
API_KEYS=key1,key2,key3
RATE_LIMIT_PER_MINUTE=60
ALLOWED_LOG_PATHS=/tmp,/var/log,./data,../oauth2-log,./oauth2-log

# ================================
# 应用基本配置
# ================================
APP_NAME=intelligent-log-analyzer
APP_VERSION=1.0.0
DEBUG=false
DATA_DIR=./data
TEMP_DIR=./temp

# ================================
# 开发环境配置
# ================================
# 开发模式下可以设置的额外变量
# DEV_MODE=true
# MOCK_DEEPSEEK=true
# MOCK_RAGFLOW=true 