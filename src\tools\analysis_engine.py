"""
深度分析引擎 - Phase 4

将搜索结果转换为结构化的问题发现，包括模式识别、根因分析和时间线重建
"""

import re
import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from collections import defaultdict, Counter

from core.models import SearchResult, Finding, FindingType, LogLevel, AnalysisContext
from core.exceptions import AnalysisError

logger = logging.getLogger(__name__)

class DeepAnalysisEngine:
    """深度分析引擎 - 智能转换SearchResult为Finding"""
    
    def __init__(self, config):
        self.config = config
        self.min_confidence_threshold = getattr(config, 'min_confidence_threshold', 0.3)
        self.pattern_analysis_enabled = getattr(config, 'pattern_analysis_enabled', True)
        self.timeline_analysis_enabled = getattr(config, 'timeline_analysis_enabled', True)
        self.root_cause_analysis_enabled = getattr(config, 'root_cause_analysis_enabled', True)
        
        logger.info("深度分析引擎初始化完成")
        logger.info(f"  - 最低置信度阈值: {self.min_confidence_threshold}")
        logger.info(f"  - 模式分析: {'启用' if self.pattern_analysis_enabled else '禁用'}")
        logger.info(f"  - 时间线分析: {'启用' if self.timeline_analysis_enabled else '禁用'}")
        logger.info(f"  - 根因分析: {'启用' if self.root_cause_analysis_enabled else '禁用'}")
    
    async def analyze_search_results(self, search_results: List[SearchResult], context: AnalysisContext) -> List[Finding]:
        """将搜索结果转换为结构化发现"""
        logger.info(f"开始深度分析 {len(search_results)} 个搜索结果")
        
        if not search_results:
            logger.warning("没有搜索结果需要分析")
            return []
        
        findings = []
        
        try:
            # Step 1: 过滤低质量结果
            filtered_results = self._filter_low_quality_results(search_results)
            logger.info(f"过滤后保留 {len(filtered_results)} 个高质量结果")
            
            # Step 2: 模式识别分析
            if self.pattern_analysis_enabled:
                # 直接使用简单模式分析
                pattern_findings = await self._simple_pattern_analysis(filtered_results)
                findings.extend(pattern_findings)
                logger.info(f"模式分析发现 {len(pattern_findings)} 个问题")
            
            # Step 3: 时间线分析
            if self.timeline_analysis_enabled:
                # 简单跳过时间线分析，避免复杂实现
                logger.info("时间线分析暂时跳过，使用简化版本")
            
            # Step 4: 根因分析
            if self.root_cause_analysis_enabled:
                # 直接使用简单根因分析
                root_cause_findings = await self._simple_root_cause_analysis(filtered_results)
                findings.extend(root_cause_findings)
                logger.info(f"根因分析发现 {len(root_cause_findings)} 个问题")
            
            # Step 5: 发现去重和优先级排序
            deduplicated_findings = self._deduplicate_findings(findings)
            sorted_findings = self._prioritize_findings(deduplicated_findings)
            
            logger.info(f"深度分析完成，共产生 {len(sorted_findings)} 个结构化发现")
            
            return sorted_findings
            
        except Exception as e:
            logger.error(f"深度分析失败: {e}")
            raise AnalysisError(f"深度分析失败: {e}")
    
    def _filter_low_quality_results(self, results: List[SearchResult]) -> List[SearchResult]:
        """过滤低质量搜索结果"""
        filtered = []
        
        for result in results:
            # 置信度过滤
            if result.confidence < self.min_confidence_threshold:
                continue
            
            # 内容质量过滤
            if len(result.matched_content.strip()) < 10:  # 太短的内容
                continue
            
            # 重复内容过滤
            if not self._is_meaningful_content(result.matched_content):
                continue
            
            filtered.append(result)
        
        return filtered
    
    async def _simple_pattern_analysis(self, results: List[SearchResult]) -> List[Finding]:
        """简单的模式分析兜底方法"""
        findings = []
        if len(results) > 10:  # 结果很多，可能是频繁错误
            from core.models import Finding, FindingType
            finding = Finding(
                type=FindingType.PATTERN,
                description=f"检测到高频问题模式：{len(results)}条相关日志",
                confidence=0.7
            )
            findings.append(finding)
        return findings
    
    async def _simple_root_cause_analysis(self, results: List[SearchResult]) -> List[Finding]:
        """简单的根因分析兜底方法"""
        findings = []
        error_keywords = ['timeout', 'connection', 'failed', 'expired']
        for keyword in error_keywords:
            matching_results = [r for r in results if keyword in r.matched_content.lower()]
            if matching_results:
                from core.models import Finding, FindingType
                finding = Finding(
                    type=FindingType.ROOT_CAUSE,
                    description=f"可能的根因：{keyword}相关问题",
                    confidence=0.6
                )
                findings.append(finding)
        return findings
    
    def _is_meaningful_content(self, content: str) -> bool:
        """判断内容是否有意义"""
        content = content.strip().lower()
        
        # 排除纯数字、纯符号等无意义内容
        if re.match(r'^[\d\s\-_.,:;]+$', content):
            return False
        
        # 排除太短的内容
        if len(content) < 10:
            return False
        
        # 必须包含关键信息标识符
        meaningful_patterns = [
            r'\d{4}-\d{2}-\d{2}',  # 日期
            r'error|exception|fail|timeout|invalid|denied',  # 错误关键词
            r'oauth|token|auth|login|bearer',  # 认证关键词
            r'redis|database|connection|service',  # 服务关键词
        ]
        
        return any(re.search(pattern, content, re.IGNORECASE) for pattern in meaningful_patterns)
    
    async def _analyze_patterns(self, results: List[SearchResult], context: AnalysisContext) -> List[Finding]:
        """模式识别分析"""
        findings = []
        
        try:
            # 错误模式分析
            error_patterns = self._identify_error_patterns(results)
            for pattern_type, pattern_results in error_patterns.items():
                if len(pattern_results) >= 2:  # 至少2个相似结果才算模式
                    finding = Finding(
                        type=FindingType.PATTERN,
                        title=f"{pattern_type}错误模式",
                        description=f"发现 {len(pattern_results)} 个 {pattern_type} 相关错误",
                        evidence=pattern_results,
                        confidence=self._calculate_pattern_confidence(pattern_results),
                        severity="HIGH" if len(pattern_results) > 5 else "MEDIUM",
                        timestamp=pattern_results[0].timestamp,
                        source_files=list(set(r.file_path for r in pattern_results))
                    )
                    findings.append(finding)
            
            # 频率模式分析
            frequency_patterns = self._analyze_frequency_patterns(results)
            findings.extend(frequency_patterns)
            
        except Exception as e:
            logger.error(f"模式分析失败: {e}")
        
        return findings
    
    async def _identify_error_patterns_dynamically(self, results: List[SearchResult], analysis_framework=None) -> Dict[str, List[SearchResult]]:
        """动态识别错误模式 - 基于分析框架和LLM推理，无硬编码"""
        patterns = defaultdict(list)
        
        try:
            # 优先使用动态分析引擎
            if hasattr(self, 'dynamic_engine') and analysis_framework:
                dynamic_patterns = await self.dynamic_engine.identify_problem_patterns(results, analysis_framework)
                
                # 将动态模式转换为结果分组
                for pattern in dynamic_patterns:
                    pattern_results = []
                    for result in results:
                        # 根据模式指标匹配结果
                        content_lower = result.matched_content.lower()
                        if any(indicator.lower() in content_lower for indicator in pattern.indicators):
                            pattern_results.append(result)
                    
                    if pattern_results:
                        patterns[pattern.pattern_name] = pattern_results
                
                return dict(patterns)
            
            # 降级：基础模式识别（无硬编码关键词）
            return await self._fallback_pattern_identification(results)
            
        except Exception as e:
            logger.error(f"动态模式识别失败: {e}")
            return await self._fallback_pattern_identification(results)
    
    async def _fallback_pattern_identification(self, results: List[SearchResult]) -> Dict[str, List[SearchResult]]:
        """降级模式识别 - 基于内容特征的通用识别"""
        patterns = defaultdict(list)
        
        # 基于内容特征分组，而非硬编码关键词
        content_groups = {
            '认证相关问题': [],
            '连接相关问题': [],
            '验证相关问题': [],
            '服务相关问题': [],
            '其他错误': []
        }
        
        for result in results:
            content_lower = result.matched_content.lower()
            categorized = False
            
            # 认证相关特征识别
            if any(word in content_lower for word in ['auth', 'token', 'login', 'permission']):
                content_groups['认证相关问题'].append(result)
                categorized = True
            
            # 连接相关特征识别
            elif any(word in content_lower for word in ['connection', 'timeout', 'refused', 'disconnect']):
                content_groups['连接相关问题'].append(result)
                categorized = True
            
            # 验证相关特征识别
            elif any(word in content_lower for word in ['invalid', 'validation', 'format', 'parse']):
                content_groups['验证相关问题'].append(result)
                categorized = True
            
            # 服务相关特征识别
            elif any(word in content_lower for word in ['service', 'unavailable', 'failed', 'error']):
                content_groups['服务相关问题'].append(result)
                categorized = True
            
            # 未分类的归入其他
            if not categorized:
                content_groups['其他错误'].append(result)
        
        # 过滤空分组
        return {k: v for k, v in content_groups.items() if v}
    
    def _analyze_frequency_patterns(self, results: List[SearchResult]) -> List[Finding]:
        """分析频率模式"""
        findings = []
        
        try:
            # 按时间窗口分组
            time_windows = self._group_by_time_windows(results, window_minutes=5)
            
            for window_start, window_results in time_windows.items():
                if len(window_results) > 10:  # 5分钟内超过10个错误
                    finding = Finding(
                        type=FindingType.FREQUENCY_ANOMALY,
                        title="高频错误爆发",
                        description=f"在 {window_start.strftime('%H:%M:%S')} 附近5分钟内发生 {len(window_results)} 个错误",
                        evidence=window_results[:10],  # 只保留前10个作为证据
                        confidence=0.8,
                        severity="CRITICAL",
                        timestamp=window_start,
                        source_files=list(set(r.file_path for r in window_results))
                    )
                    findings.append(finding)
        
        except Exception as e:
            logger.error(f"频率模式分析失败: {e}")
        
        return findings
    
    def _group_by_time_windows(self, results: List[SearchResult], window_minutes: int = 5) -> Dict[datetime, List[SearchResult]]:
        """按时间窗口分组"""
        windows = defaultdict(list)
        
        for result in results:
            if result.timestamp:
                # 将时间戳向下取整到window_minutes分钟
                window_start = result.timestamp.replace(
                    minute=(result.timestamp.minute // window_minutes) * window_minutes,
                    second=0,
                    microsecond=0
                )
                windows[window_start].append(result)
        
        return dict(windows)
    
    async def _analyze_timeline(self, results: List[SearchResult], context: AnalysisContext) -> List[Finding]:
        """时间线分析"""
        findings = []
        
        try:
            # 排序结果按时间
            time_sorted_results = [r for r in results if r.timestamp]
            time_sorted_results.sort(key=lambda x: x.timestamp)
            
            if len(time_sorted_results) < 2:
                return findings
            
            # 检测时间异常
            time_gaps = self._detect_time_gaps(time_sorted_results)
            findings.extend(time_gaps)
            
            # 检测事件序列
            event_sequences = self._detect_event_sequences(time_sorted_results)
            findings.extend(event_sequences)
            
        except Exception as e:
            logger.error(f"时间线分析失败: {e}")
        
        return findings
    
    def _detect_time_gaps(self, results: List[SearchResult]) -> List[Finding]:
        """检测时间间隔异常"""
        findings = []
        
        for i in range(1, len(results)):
            time_diff = results[i].timestamp - results[i-1].timestamp
            
            # 检测异常时间间隔（如果两个相关错误间隔太近）
            if time_diff.total_seconds() < 1 and self._are_related_errors(results[i-1], results[i]):
                finding = Finding(
                    type=FindingType.TEMPORAL_ANOMALY,
                    title="并发错误检测",
                    description=f"在{time_diff.total_seconds():.3f}秒内发生连续相关错误",
                    evidence=[results[i-1], results[i]],
                    confidence=0.7,
                    severity="HIGH",
                    timestamp=results[i-1].timestamp,
                    source_files=[results[i-1].file_path, results[i].file_path]
                )
                findings.append(finding)
        
        return findings
    
    def _are_related_errors(self, result1: SearchResult, result2: SearchResult) -> bool:
        """判断两个错误是否相关"""
        # 检查关键词相似性
        keywords1 = set(re.findall(r'\w+', result1.matched_content.lower()))
        keywords2 = set(re.findall(r'\w+', result2.matched_content.lower()))
        
        # 计算关键词交集
        intersection = keywords1 & keywords2
        union = keywords1 | keywords2
        
        if not union:
            return False
        
        similarity = len(intersection) / len(union)
        return similarity > 0.3  # 30%以上关键词相同
    
    def _detect_event_sequences(self, results: List[SearchResult]) -> List[Finding]:
        """检测事件序列模式"""
        findings = []
        
        # OAuth2 token相关序列检测
        oauth_sequence = self._detect_oauth_sequence(results)
        if oauth_sequence:
            findings.extend(oauth_sequence)
        
        return findings
    
    def _detect_oauth_sequence(self, results: List[SearchResult]) -> List[Finding]:
        """检测OAuth2认证序列"""
        findings = []
        oauth_events = []
        
        # 识别OAuth2相关事件
        for result in results:
            content_lower = result.matched_content.lower()
            if any(keyword in content_lower for keyword in ['oauth', 'token', 'auth', 'bearer']):
                oauth_events.append(result)
        
        if len(oauth_events) >= 3:  # 至少3个相关事件
            finding = Finding(
                type=FindingType.SEQUENCE,
                title="OAuth2认证流程分析",
                description=f"发现 {len(oauth_events)} 个OAuth2相关事件的完整序列",
                evidence=oauth_events,
                confidence=0.8,
                severity="MEDIUM",
                timestamp=oauth_events[0].timestamp,
                source_files=list(set(e.file_path for e in oauth_events))
            )
            findings.append(finding)
        
        return findings
    
    async def _analyze_root_causes(self, results: List[SearchResult], context: AnalysisContext) -> List[Finding]:
        """根因分析"""
        findings = []
        
        try:
            # 分析根本原因模式
            root_causes = self._identify_root_causes(results)
            
            for cause_type, cause_results in root_causes.items():
                if len(cause_results) >= 1:
                    finding = Finding(
                        type=FindingType.ROOT_CAUSE,
                        title=f"根因识别: {cause_type}",
                        description=self._generate_root_cause_description(cause_type, cause_results),
                        evidence=cause_results,
                        confidence=self._calculate_root_cause_confidence(cause_type, cause_results),
                        severity=self._determine_root_cause_severity(cause_type, cause_results),
                        timestamp=cause_results[0].timestamp,
                        source_files=list(set(r.file_path for r in cause_results))
                    )
                    findings.append(finding)
        
        except Exception as e:
            logger.error(f"根因分析失败: {e}")
        
        return findings
    
    async def _identify_root_causes_intelligently(self, results: List[SearchResult], analysis_framework=None) -> Dict[str, List[SearchResult]]:
        """智能识别根本原因 - 基于LLM推理，无硬编码"""
        root_causes = defaultdict(list)
        
        try:
            # 优先使用动态分析引擎的智能根因分析
            if hasattr(self, 'dynamic_engine') and analysis_framework:
                # 先识别模式
                patterns = await self._identify_error_patterns_dynamically(results, analysis_framework)
                pattern_objects = []
                
                # 将模式转换为动态模式对象
                for pattern_name, pattern_results in patterns.items():
                    from tools.dynamic_analysis_engine import DynamicPattern
                    pattern_obj = DynamicPattern(
                        pattern_name=pattern_name,
                        pattern_type="error",
                        indicators=[],
                        confidence=0.8,
                        evidence=[r.matched_content[:100] for r in pattern_results[:3]],
                        source="pattern_analysis"
                    )
                    pattern_objects.append(pattern_obj)
                
                # 使用动态引擎推理根因
                intelligent_causes = await self.dynamic_engine.reason_root_causes(
                    pattern_objects, analysis_framework, results
                )
                
                # 将智能根因转换为结果分组
                for cause in intelligent_causes:
                    cause_results = []
                    for result in results:
                        # 根据证据链匹配相关结果
                        content_lower = result.matched_content.lower()
                        if any(evidence.lower() in content_lower for evidence in cause.evidence_chain):
                            cause_results.append(result)
                    
                    if cause_results:
                        root_causes[cause.cause_name] = cause_results
                
                return dict(root_causes)
            
            # 降级：基础根因推理
            return await self._fallback_root_cause_analysis(results)
            
        except Exception as e:
            logger.error(f"智能根因分析失败: {e}")
            return await self._fallback_root_cause_analysis(results)
    
    async def _fallback_root_cause_analysis(self, results: List[SearchResult]) -> Dict[str, List[SearchResult]]:
        """降级根因分析 - 基于通用逻辑推理"""
        root_causes = defaultdict(list)
        
        # 基于常见错误特征的通用分类
        cause_categories = {
            '时间相关问题': [],
            '权限相关问题': [],
            '连接相关问题': [],
            '配置相关问题': [],
            '资源相关问题': [],
            '其他根因': []
        }
        
        for result in results:
            content_lower = result.matched_content.lower()
            categorized = False
            
            # 时间相关问题特征
            if any(word in content_lower for word in ['expired', 'timeout', 'time', 'duration']):
                cause_categories['时间相关问题'].append(result)
                categorized = True
            
            # 权限相关问题特征
            elif any(word in content_lower for word in ['permission', 'denied', 'unauthorized', 'access']):
                cause_categories['权限相关问题'].append(result)
                categorized = True
            
            # 连接相关问题特征
            elif any(word in content_lower for word in ['connection', 'refused', 'disconnect', 'network']):
                cause_categories['连接相关问题'].append(result)
                categorized = True
            
            # 配置相关问题特征
            elif any(word in content_lower for word in ['config', 'setting', 'property', 'parameter']):
                cause_categories['配置相关问题'].append(result)
                categorized = True
            
            # 资源相关问题特征
            elif any(word in content_lower for word in ['memory', 'resource', 'limit', 'capacity']):
                cause_categories['资源相关问题'].append(result)
                categorized = True
            
            # 未分类的
            if not categorized:
                cause_categories['其他根因'].append(result)
        
        # 过滤空分组
        return {k: v for k, v in cause_categories.items() if v}
    
    def _generate_root_cause_description(self, cause_type: str, results: List[SearchResult]) -> str:
        """生成根因描述"""
        descriptions = {
            'Token过期': f"检测到 {len(results)} 个Token过期相关错误，可能是认证令牌生命周期配置问题",
            'Token覆盖': f"发现 {len(results)} 个Token被覆盖的情况，可能存在并发登录或重复认证问题",
            'Redis连接': f"识别到 {len(results)} 个Redis连接相关错误，可能是缓存服务不稳定",
            '权限不足': f"发现 {len(results)} 个权限相关错误，可能是访问控制配置问题",
            '并发冲突': f"检测到 {len(results)} 个并发相关错误，可能是高并发场景下的资源竞争",
            '配置错误': f"识别到 {len(results)} 个配置相关错误，可能是系统配置不当",
        }
        
        return descriptions.get(cause_type, f"发现 {len(results)} 个 {cause_type} 相关问题")
    
    def _calculate_pattern_confidence(self, results: List[SearchResult]) -> float:
        """计算模式置信度"""
        if not results:
            return 0.0
        
        # 基于结果数量和平均置信度
        avg_confidence = sum(r.confidence_score for r in results) / len(results)
        count_bonus = min(len(results) * 0.1, 0.3)  # 数量奖励，最大0.3
        
        return min(avg_confidence + count_bonus, 1.0)
    
    def _calculate_root_cause_confidence(self, cause_type: str, results: List[SearchResult]) -> float:
        """计算根因置信度"""
        base_confidence = sum(r.confidence_score for r in results) / len(results)
        
        # 根据根因类型调整置信度
        confidence_modifiers = {
            'Token过期': 0.9,
            'Token覆盖': 0.8,
            'Redis连接': 0.85,
            '权限不足': 0.8,
            '并发冲突': 0.7,
            '配置错误': 0.75,
        }
        
        modifier = confidence_modifiers.get(cause_type, 0.7)
        return min(base_confidence * modifier, 1.0)
    
    def _determine_root_cause_severity(self, cause_type: str, results: List[SearchResult]) -> str:
        """确定根因严重程度"""
        # 基于影响范围和结果数量
        severity_map = {
            'Token过期': "HIGH",
            'Token覆盖': "CRITICAL",
            'Redis连接': "HIGH", 
            '权限不足': "MEDIUM",
            '并发冲突': "HIGH",
            '配置错误': "MEDIUM",
        }
        
        base_severity = severity_map.get(cause_type, "MEDIUM")
        
        # 如果影响结果很多，提升严重程度
        if len(results) > 10:
            if base_severity == "MEDIUM":
                return "HIGH"
            elif base_severity == "HIGH":
                return "CRITICAL"
        
        return base_severity
    
    def _deduplicate_findings(self, findings: List[Finding]) -> List[Finding]:
        """去除重复发现"""
        unique_findings = []
        seen_signatures = set()
        
        for finding in findings:
            # 生成发现签名
            signature = (finding.type, finding.title, len(finding.evidence))
            
            if signature not in seen_signatures:
                seen_signatures.add(signature)
                unique_findings.append(finding)
        
        return unique_findings
    
    def _prioritize_findings(self, findings: List[Finding]) -> List[Finding]:
        """按优先级排序发现"""
        severity_order = {"CRITICAL": 4, "HIGH": 3, "MEDIUM": 2, "LOW": 1}
        
        findings.sort(
            key=lambda f: (
                severity_order.get(f.severity, 0),
                f.confidence,
                len(f.evidence)
            ),
            reverse=True
        )
        
        return findings 