---
globs: *.py
---

# Python Code Quality Standards

## Code Organization
- Split files over 500 lines into focused modules
- Use clear module structure: models, tools, config, core
- Group related functionality together

## Configuration Management
- Use Pydantic BaseSettings for configuration
- Support environment variables and .env files
- Include validators for critical settings
- Never hardcode credentials or endpoints

## Error Handling
- Create custom exception hierarchies
- Implement graceful degradation
- Use try-except with specific exception types
- Log errors appropriately

## Async Patterns
- Use async/await for I/O operations
- Implement proper timeout handling
- Use context managers for resource cleanup
- Batch operations when possible

## Import Management
- Use optional imports with fallbacks for dependencies
- Group imports: stdlib, third-party, local
- Implement graceful degradation when modules unavailable

## Documentation
- Use clear docstrings for public methods
- Include type hints for better IDE support
- Avoid excessive inline comments
- Focus on explaining 'why' not 'what'

## Testing
- Create integration tests for core workflows
- Test error conditions and edge cases
- Use meaningful test data
- Validate configurations separately
