# 智能日志分析器架构设计文档

## 总体架构

### 架构原则
1. **基于ADK FunctionTool**: 利用已验证的100%可用技术
2. **知识库驱动**: RAGFlow提供分析方法，减少硬编码
3. **模块化设计**: 高内聚、低耦合的组件设计
4. **可扩展性**: 支持新的日志类型和分析方法

### 技术栈
- **LLM框架**: Google ADK Python + DeepSeek-R1
- **搜索引擎**: ripgrep + 自定义索引
- **知识库**: RAGFlow API
- **数据处理**: pandas + 自定义解析器
- **接口**: FastAPI + Streamlit + CLI

## 模块架构图

```
intelligent-log-analyzer/
├── src/
│   ├── core/                   # 核心模块
│   │   ├── __init__.py
│   │   ├── agent.py           # 主Agent控制器
│   │   ├── models.py          # 数据模型定义
│   │   ├── exceptions.py      # 异常定义
│   │   └── session.py         # 会话管理
│   │
│   ├── tools/                  # FunctionTool工具集
│   │   ├── __init__.py
│   │   ├── ragflow_client.py  # RAGFlow客户端
│   │   ├── log_search.py      # 日志搜索工具
│   │   ├── log_reader.py      # 智能日志读取
│   │   ├── file_ops.py        # 文件操作工具
│   │   └── analysis_tools.py  # 分析辅助工具
│   │
│   ├── agents/                 # Agent实现
│   │   ├── __init__.py
│   │   ├── log_analysis_agent.py  # 主分析Agent
│   │   ├── deepseek_llm.py        # DeepSeek LLM封装
│   │   └── workflow_engine.py     # 工作流引擎
│   │
│   ├── utils/                  # 工具类
│   │   ├── __init__.py
│   │   ├── log_parser.py      # 日志解析工具
│   │   ├── indexer.py         # 索引工具
│   │   ├── file_utils.py      # 文件工具
│   │   └── time_utils.py      # 时间工具
│   │
│   └── config/                 # 配置管理
│       ├── __init__.py
│       ├── settings.py        # 配置设置
│       └── prompts.py         # 提示词模板
│
├── tests/                      # 测试
│   ├── unit/                  # 单元测试
│   └── integration/           # 集成测试
│
├── data/                       # 数据目录
│   ├── logs/                  # 日志文件
│   ├── knowledge_base/        # 知识库文件
│   └── reports/               # 分析报告
│
├── scripts/                    # 脚本工具
│   ├── setup.py              # 环境设置
│   ├── decompress.py         # 日志解压
│   └── build_index.py        # 索引构建
│
└── docs/                       # 文档
    ├── PRD.md
    ├── ARCHITECTURE.md
    └── API.md
```

## 核心类设计

### 1. 数据模型层 (core/models.py)

```python
from dataclasses import dataclass
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum

class AnalysisState(Enum):
    """分析状态枚举"""
    INITIALIZED = "initialized"
    SEARCHING = "searching"
    READING = "reading"
    ANALYZING = "analyzing"
    CONCLUDED = "concluded"
    FAILED = "failed"

class FindingType(Enum):
    """发现类型枚举"""
    ROOT_CAUSE = "root_cause"
    EVIDENCE = "evidence"
    TIMELINE = "timeline"
    RECOMMENDATION = "recommendation"

@dataclass
class SearchQuery:
    """搜索查询模型"""
    keywords: List[str]
    time_range: Optional[tuple[datetime, datetime]] = None
    file_pattern: Optional[str] = None
    context_lines: int = 10
    max_results: int = 100

@dataclass
class SearchResult:
    """搜索结果模型"""
    file_path: str
    line_number: int
    matched_content: str
    context_before: List[str]
    context_after: List[str]
    confidence_score: float
    timestamp: Optional[datetime] = None

@dataclass
class Evidence:
    """证据模型"""
    type: str
    description: str
    source_file: str
    line_number: int
    content: str
    timestamp: Optional[datetime] = None
    confidence: float = 1.0

@dataclass
class Finding:
    """分析发现模型"""
    type: FindingType
    title: str
    description: str
    evidence: List[Evidence]
    confidence: float
    timestamp: datetime

@dataclass
class AnalysisContext:
    """分析上下文模型"""
    problem_description: str
    analysis_method: Optional[str] = None
    current_step: str = "initialized"
    search_results: List[SearchResult] = None
    findings: List[Finding] = None
    analysis_state: AnalysisState = AnalysisState.INITIALIZED
    session_id: str = ""
    created_at: datetime = None
    
    def __post_init__(self):
        if self.search_results is None:
            self.search_results = []
        if self.findings is None:
            self.findings = []
        if self.created_at is None:
            self.created_at = datetime.now()

@dataclass
class AnalysisReport:
    """分析报告模型"""
    problem: str
    summary: str
    root_cause: Optional[str]
    timeline: List[Dict[str, Any]]
    evidence: List[Evidence]
    recommendations: List[str]
    confidence_score: float
    analysis_duration: float
    files_analyzed: List[str]
    generated_at: datetime
```

### 2. 核心Agent层 (core/agent.py)

```python
from typing import AsyncGenerator, List, Optional
from google.adk.agents import LlmAgent
from google.adk.tools import FunctionTool
from google.adk.sessions import InMemorySessionService
from google.adk.memory import InMemoryMemoryService

from .models import AnalysisContext, AnalysisReport, AnalysisState
from ..tools.ragflow_client import RAGFlowClient
from ..tools.log_search import LogSearchEngine
from ..agents.deepseek_llm import DeepSeekCompatibleLLM

class LogAnalysisAgent:
    """智能日志分析主控制器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.llm = DeepSeekCompatibleLLM(config.get('deepseek'))
        self.ragflow_client = RAGFlowClient(config.get('ragflow'))
        self.search_engine = LogSearchEngine(config.get('search'))
        self.session_service = InMemorySessionService()
        self.memory_service = InMemoryMemoryService()
        
        # 初始化ADK Agent
        self.adk_agent = LlmAgent(
            model=self.llm,
            name="log_analysis_agent",
            description="智能日志分析Agent",
            tools=self._setup_function_tools(),
            session_service=self.session_service,
            memory_service=self.memory_service
        )
    
    def _setup_function_tools(self) -> List[FunctionTool]:
        """设置FunctionTool工具集"""
        return [
            FunctionTool(self.ragflow_query_method),
            FunctionTool(self.search_logs),
            FunctionTool(self.read_log_content),
            FunctionTool(self.update_analysis_state),
            FunctionTool(self.evaluate_evidence)
        ]
    
    async def analyze_problem(
        self, 
        problem: str, 
        log_directory: str
    ) -> AnalysisReport:
        """主分析流程入口"""
        
        # 1. 初始化分析上下文
        context = AnalysisContext(
            problem_description=problem,
            session_id=f"analysis_{datetime.now().isoformat()}"
        )
        
        # 2. 创建会话
        session = await self.session_service.create_session(
            app_name="log_analyzer",
            user_id="analyst",
            session_id=context.session_id
        )
        
        # 3. 执行分析工作流
        report = await self._execute_analysis_workflow(context, log_directory)
        
        return report
    
    async def _execute_analysis_workflow(
        self, 
        context: AnalysisContext, 
        log_directory: str
    ) -> AnalysisReport:
        """执行分析工作流"""
        
        try:
            # Phase 1: 获取分析方法
            await self._phase_get_analysis_method(context)
            
            # Phase 2: 搜索相关日志
            await self._phase_search_logs(context, log_directory)
            
            # Phase 3: 多轮分析对话
            await self._phase_multi_turn_analysis(context)
            
            # Phase 4: 生成分析报告
            report = await self._phase_generate_report(context)
            
            return report
            
        except Exception as e:
            context.analysis_state = AnalysisState.FAILED
            raise AnalysisException(f"分析失败: {str(e)}")
    
    # FunctionTool 实现方法
    async def ragflow_query_method(self, problem: str) -> str:
        """RAGFlow查询分析方法"""
        return await self.ragflow_client.query_analysis_method(problem)
    
    async def search_logs(self, keywords: List[str], log_dir: str) -> List[Dict]:
        """搜索日志文件"""
        results = await self.search_engine.search(keywords, log_dir)
        return [result.__dict__ for result in results]
    
    async def read_log_content(self, file_path: str, line_range: tuple) -> str:
        """读取日志内容"""
        return await self.search_engine.read_log_content(file_path, line_range)
    
    async def update_analysis_state(self, findings: List[Dict]) -> str:
        """更新分析状态"""
        # 实现状态更新逻辑
        return "状态已更新"
    
    async def evaluate_evidence(self, evidence_list: List[Dict]) -> float:
        """评估证据充分性"""
        # 实现证据评估逻辑
        return 0.85  # 示例置信度
```

### 3. 工具层设计

#### RAGFlow客户端 (tools/ragflow_client.py)

```python
import httpx
from typing import Optional, Dict, Any

class RAGFlowClient:
    """RAGFlow知识库客户端"""
    
    def __init__(self, config: Dict[str, Any]):
        self.base_url = config.get('base_url')
        self.api_key = config.get('api_key')
        self.conversation_id = config.get('conversation_id')
        
    async def query_analysis_method(self, problem: str) -> Dict[str, Any]:
        """查询分析方法"""
        
        query = f"{problem}的分析方法是什么？排查步骤是什么？"
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/v1/api/conversation/completion",
                headers={
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "conversation_id": self.conversation_id,
                    "messages": [{"role": "user", "content": query}],
                    "stream": False
                }
            )
            
        result = response.json()
        
        return {
            "method": result.get("data", {}).get("answer", ""),
            "confidence": self._extract_confidence(result),
            "has_method": bool(result.get("data", {}).get("reference"))
        }
    
    def _extract_confidence(self, result: Dict) -> float:
        """提取置信度"""
        references = result.get("data", {}).get("reference", [])
        if references:
            return max(ref.get("similarity", 0.0) for ref in references)
        return 0.0
```

#### 日志搜索引擎 (tools/log_search.py)

```python
import subprocess
import asyncio
from pathlib import Path
from typing import List, Dict, Optional
from ..core.models import SearchQuery, SearchResult

class LogSearchEngine:
    """高性能日志搜索引擎"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.ripgrep_path = config.get('ripgrep_path', 'rg')
        self.max_context_lines = config.get('max_context_lines', 10)
        
    async def search(
        self, 
        keywords: List[str], 
        log_directory: str
    ) -> List[SearchResult]:
        """多关键词搜索"""
        
        search_results = []
        
        for keyword in keywords:
            results = await self._ripgrep_search(keyword, log_directory)
            search_results.extend(results)
        
        # 去重并按置信度排序
        unique_results = self._deduplicate_results(search_results)
        sorted_results = sorted(unique_results, key=lambda x: x.confidence_score, reverse=True)
        
        return sorted_results[:self.config.get('max_results', 100)]
    
    async def _ripgrep_search(
        self, 
        keyword: str, 
        directory: str
    ) -> List[SearchResult]:
        """使用ripgrep进行搜索"""
        
        cmd = [
            self.ripgrep_path,
            '--line-number',
            '--with-filename',
            '--context', str(self.max_context_lines),
            '--json',
            keyword,
            directory
        ]
        
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await process.communicate()
        
        if process.returncode != 0:
            raise SearchException(f"ripgrep搜索失败: {stderr.decode()}")
        
        return self._parse_ripgrep_output(stdout.decode())
    
    def _parse_ripgrep_output(self, output: str) -> List[SearchResult]:
        """解析ripgrep输出"""
        results = []
        
        for line in output.strip().split('\n'):
            if not line:
                continue
                
            try:
                data = json.loads(line)
                if data.get('type') == 'match':
                    result = self._build_search_result(data)
                    if result:
                        results.append(result)
            except json.JSONDecodeError:
                continue
        
        return results
    
    def _build_search_result(self, match_data: Dict) -> Optional[SearchResult]:
        """构建搜索结果对象"""
        
        data = match_data.get('data', {})
        
        return SearchResult(
            file_path=data.get('path', {}).get('text', ''),
            line_number=data.get('line_number', 0),
            matched_content=data.get('lines', {}).get('text', ''),
            context_before=[],  # 需要从上下文中提取
            context_after=[],   # 需要从上下文中提取
            confidence_score=self._calculate_confidence(data),
            timestamp=self._extract_timestamp(data.get('lines', {}).get('text', ''))
        )
    
    async def read_log_content(
        self, 
        file_path: str, 
        line_range: tuple, 
        max_chars: int = 8000
    ) -> str:
        """智能读取日志内容"""
        
        start_line, end_line = line_range
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            # 确保行号范围有效
            start_idx = max(0, start_line - 1)
            end_idx = min(len(lines), end_line)
            
            content_lines = lines[start_idx:end_idx]
            content = ''.join(content_lines)
            
            # 限制内容长度
            if len(content) > max_chars:
                content = content[:max_chars] + "\n... [内容截断，需要分段读取] ..."
            
            return content
            
        except Exception as e:
            raise FileReadException(f"读取文件失败 {file_path}: {str(e)}")
```

### 4. 接口设计

#### FastAPI REST接口 (api/main.py)

```python
from fastapi import FastAPI, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import List, Optional
import uuid

app = FastAPI(title="智能日志分析器API", version="1.0.0")

class AnalysisRequest(BaseModel):
    problem: str
    log_directory: str
    priority: Optional[str] = "normal"

class AnalysisResponse(BaseModel):
    analysis_id: str
    status: str
    message: str

class AnalysisStatusResponse(BaseModel):
    analysis_id: str
    status: str
    progress: float
    current_step: str
    estimated_time_remaining: Optional[int]

class AnalysisReportResponse(BaseModel):
    analysis_id: str
    problem: str
    summary: str
    root_cause: Optional[str]
    recommendations: List[str]
    confidence_score: float
    generated_at: str

@app.post("/analyze", response_model=AnalysisResponse)
async def start_analysis(
    request: AnalysisRequest,
    background_tasks: BackgroundTasks
):
    """启动日志分析任务"""
    
    analysis_id = str(uuid.uuid4())
    
    # 添加后台任务
    background_tasks.add_task(
        run_analysis,
        analysis_id,
        request.problem,
        request.log_directory
    )
    
    return AnalysisResponse(
        analysis_id=analysis_id,
        status="started",
        message="分析任务已启动"
    )

@app.get("/analyze/{analysis_id}/status", response_model=AnalysisStatusResponse)
async def get_analysis_status(analysis_id: str):
    """获取分析状态"""
    
    # 从数据库或缓存获取状态
    status_info = await get_analysis_status_from_store(analysis_id)
    
    if not status_info:
        raise HTTPException(status_code=404, detail="分析任务不存在")
    
    return AnalysisStatusResponse(**status_info)

@app.get("/analyze/{analysis_id}/report", response_model=AnalysisReportResponse)
async def get_analysis_report(analysis_id: str):
    """获取分析报告"""
    
    report = await get_analysis_report_from_store(analysis_id)
    
    if not report:
        raise HTTPException(status_code=404, detail="分析报告不存在")
    
    return AnalysisReportResponse(**report)

async def run_analysis(analysis_id: str, problem: str, log_directory: str):
    """后台分析任务"""
    
    try:
        agent = LogAnalysisAgent(config=load_config())
        report = await agent.analyze_problem(problem, log_directory)
        
        # 保存报告到存储
        await save_analysis_report(analysis_id, report)
        
    except Exception as e:
        await save_analysis_error(analysis_id, str(e))
```

### 5. 配置管理 (config/settings.py)

```python
from pydantic import BaseSettings, Field
from typing import Optional

class DeepSeekConfig(BaseSettings):
    api_base: str = Field(..., env="DEEPSEEK_API_BASE")
    api_key: str = Field(..., env="DEEPSEEK_API_KEY") 
    model: str = Field("deepseek/deepseek-r1", env="DEEPSEEK_MODEL")
    temperature: float = Field(0.6, env="DEEPSEEK_TEMPERATURE")
    max_tokens: int = Field(8192, env="DEEPSEEK_MAX_TOKENS")

class RAGFlowConfig(BaseSettings):
    base_url: str = Field(..., env="RAGFLOW_BASE_URL")
    api_key: str = Field(..., env="RAGFLOW_API_KEY")
    conversation_id: str = Field(..., env="RAGFLOW_CONVERSATION_ID")

class SearchConfig(BaseSettings):
    ripgrep_path: str = Field("rg", env="RIPGREP_PATH")
    max_results: int = Field(100, env="SEARCH_MAX_RESULTS")
    max_context_lines: int = Field(10, env="SEARCH_CONTEXT_LINES")
    max_file_size_mb: int = Field(100, env="MAX_FILE_SIZE_MB")

class AppConfig(BaseSettings):
    deepseek: DeepSeekConfig = DeepSeekConfig()
    ragflow: RAGFlowConfig = RAGFlowConfig()
    search: SearchConfig = SearchConfig()
    
    log_level: str = Field("INFO", env="LOG_LEVEL")
    max_concurrent_analyses: int = Field(5, env="MAX_CONCURRENT_ANALYSES")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

def load_config() -> AppConfig:
    """加载配置"""
    return AppConfig()
```

## 接口定义总结

### 1. 核心接口
- `LogAnalysisAgent.analyze_problem()`: 主分析入口
- `RAGFlowClient.query_analysis_method()`: 知识库查询
- `LogSearchEngine.search()`: 日志搜索
- `LogSearchEngine.read_log_content()`: 内容读取

### 2. REST API接口
- `POST /analyze`: 启动分析
- `GET /analyze/{id}/status`: 获取状态  
- `GET /analyze/{id}/report`: 获取报告

### 3. FunctionTool接口
- `ragflow_query_method()`: RAGFlow查询
- `search_logs()`: 日志搜索
- `read_log_content()`: 内容读取
- `update_analysis_state()`: 状态更新
- `evaluate_evidence()`: 证据评估

这个架构设计充分利用了已验证的DeepSeek+ADK兼容性，通过FunctionTool模式实现可靠的大规模日志分析能力，确保项目的技术可行性和成功实施。 