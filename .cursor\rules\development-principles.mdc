---
alwaysApply: true
---

# Core Development Principles

## Architecture Principles
- **DRY Principle**: Eliminate duplicate code, extract common components
- **KISS Principle**: Keep it simple, prioritize MVP implementation
- **First Principles**: Start from functional essence, not existing code constraints
- **Systems Thinking**: Consider global impact when addressing specific issues

## Code Quality Standards
- **No Hardcoding**: Externalize configuration for better generalizability
- **No Emoji Symbols**: Keep code clean and professional
- **No Long Files**: Split files exceeding 500 lines
- **In-Place Modifications**: Prefer editing existing files over creating new ones

## File Management Strategy

### Priority Order
Edit Existing > Refactor/Split > Create New File

### File Creation Rules
- Only create files when absolutely necessary
- Avoid temporary files and unnecessary prefixes/suffixes
- Don't proactively create documentation files unless explicitly requested
- Always prefer editing existing files over creating new ones

## Development Workflow

### Three-Stage Development Process
1. **Analyze Problem** - Understand requirements, think systemically
2. **Refine Solution** - List changed files and descriptions
3. **Execute Solution** - Actual code implementation

### Mandatory Checkpoints
- Declare current stage at response beginning
- Behavior matches stage requirements
- Get explicit user consent before stage switching

## Quality Assurance

### Long-term Considerations
- Evaluate technical debt
- Consider maintenance costs
- Design for extensibility

### Code Duplication Detection
- Must identify duplicate code when found
- Extract common logic
- Build reusable components

### MVP-Oriented Development
- Core functionality first
- Rapid feasibility validation
- Iterative improvement
