---
globs: **/config/**,**/settings/**,*.env*,config.py
---

# Configuration Management Guidelines

## Configuration Structure
- Use Pydantic BaseSettings for type safety and validation
- Group related settings into nested configuration classes
- Support both environment variables and .env files
- Provide sensible defaults where possible

## Security Practices
- Never hardcode API keys, tokens, or credentials
- Use environment variables for sensitive information
- Provide example configurations without real credentials
- Validate required credentials at startup

## Environment Management
- Support development, testing, and production environments
- Use environment-specific overrides
- Document all configuration options
- Implement configuration validation

## External Service Configuration
- Include timeout and retry settings
- Provide fallback configurations
- Test connectivity during initialization
- Support optional services with graceful degradation

## Validation and Error Handling
- Validate configuration at application startup
- Provide clear error messages for missing/invalid settings
- Use validators for complex configuration rules
- Support configuration file reloading when appropriate

## Documentation Requirements
- Document all configuration options
- Provide example configurations
- Explain the purpose of each setting
- Include troubleshooting guidance
