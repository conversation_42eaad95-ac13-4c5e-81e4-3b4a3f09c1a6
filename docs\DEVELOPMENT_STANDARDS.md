# 智能日志分析器开发标准与规范

## 🎯 核心设计原则

### 架构原则
- **DRY原则**：消除重复代码，提取公共组件
- **KISS原则**：保持简单，优先MVP实现
- **第一性原理**：从功能本质出发，不被现有代码束缚
- **系统性思维**：单点问题考虑全局影响

### 代码质量标准
- **配置外部化**：避免硬编码，提高通用性
- **代码简洁性**：避免emoji符号，保持专业
- **文件大小控制**：超过500行需拆分模块
- **就地修改优先**：编辑现有文件 > 创建新文件

## 🏗️ Google ADK架构最佳实践

### Agent架构模式

基于Google ADK官方源码分析的核心模式：

#### 1. BaseAgent层次化结构
```python
from google.adk.agents import BaseAgent, LlmAgent

class LogAnalysisAgent(BaseAgent):
    """智能日志分析Agent - 遵循ADK官方架构"""
    
    def __init__(self, config: AppConfig):
        super().__init__(
            name="log_analysis_agent",
            description="Intelligent log analysis with DeepSeek and RAGFlow"
        )
        self.config = config
        # 初始化子组件
```

#### 2. LlmAgent标准配置
```python
# 参考官方示例：adk-samples/python/agents/software-bug-assistant
root_agent = Agent(
    model=DeepSeekLiteLlm(model="openai/DeepSeek-V3-0324"),
    name="log_analyzer",
    instruction=analysis_instruction,
    tools=[search_tool, ragflow_tool, file_manager_tool],
    generate_content_config=types.GenerateContentConfig(
        temperature=0.6,
        max_output_tokens=4096
    )
)
```

#### 3. InvocationContext管理
```python
# 遵循ADK官方模式
async def _run_async_impl(self, ctx: InvocationContext) -> AsyncGenerator[Event, None]:
    # 使用官方上下文管理模式
    with tracer.start_as_current_span(f'agent_run [{self.name}]'):
        # 分析逻辑实现
        yield Event(
            invocation_id=ctx.invocation_id,
            author=self.name,
            content=types.Content(...)
        )
```

### Runner执行模式

#### 标准Runner配置
```python
# 参考ADK官方模式
runner = Runner(
    app_name="intelligent-log-analyzer",
    agent=root_agent,
    session_service=InMemorySessionService(),
    memory_service=InMemoryMemoryService(),
    artifact_service=InMemoryArtifactService()
)
```

## 🔧 工具系统设计模式

### BaseTool标准实现

#### 1. FunctionTool包装模式
```python
from google.adk.tools import FunctionTool

# 标准工具包装 - 遵循ADK官方模式
def log_search_function(keywords: List[str], file_path: str = None) -> Dict[str, Any]:
    """搜索日志文件中的关键词"""
    # 具体实现
    pass

search_tool = FunctionTool(
    name="log_search",
    description="在日志文件中搜索关键词",
    func=log_search_function
)
```

#### 2. 自定义工具实现
```python
from google.adk.tools import BaseTool

class RAGFlowTool(BaseTool):
    """RAGFlow知识库查询工具 - 遵循ADK架构"""
    
    def __init__(self, client: RAGFlowClient):
        super().__init__(
            name="ragflow_query",
            description="查询RAGFlow知识库获取分析方法"
        )
        self.client = client
    
    async def run_async(self, *, args: dict[str, Any], tool_context: ToolContext) -> Any:
        question = args.get("question", "")
        return await self.client.query_knowledge(question)
    
    def _get_declaration(self) -> types.FunctionDeclaration:
        return types.FunctionDeclaration(
            name=self.name,
            description=self.description,
            parameters=types.Schema(
                type=types.Type.OBJECT,
                properties={
                    "question": types.Schema(type=types.Type.STRING)
                },
                required=["question"]
            )
        )
```

## 📋 System Prompts与模型设计

### 提示工程最佳实践

#### 1. 指令设计模式
```python
# 参考开源智能体提示设计
ANALYSIS_INSTRUCTION = """
你是一个专业的日志分析专家，专门分析新能源汽车后台系统的日志问题。

核心能力：
1. 智能搜索：使用关键词在大量日志中精确定位问题
2. 上下文理解：分析日志的时间序列和因果关系
3. 根本原因分析：识别问题的本质原因而非表象
4. 解决方案建议：提供具体可行的修复建议

分析流程：
1. 理解问题描述和分析目标
2. 查询知识库获取分析方法和经验
3. 智能搜索相关日志内容
4. 分析日志上下文和时间线
5. 识别根本原因并提供解决方案

工具使用原则：
- 使用log_search搜索关键词
- 使用ragflow_query获取分析方法
- 使用file_manager处理日志文件
- 优先使用已有经验和模式

保持专业、准确、高效的分析风格。
"""

# 多模型协作提示
REASONING_INSTRUCTION = """
作为推理专家，深度分析日志中的因果关系：

<think>
1. 分析日志时间序列
2. 识别异常模式
3. 推理因果关系
4. 验证假设
</think>

基于深度推理提供结论。
"""
```

#### 2. 结构化输出模式
```python
from pydantic import BaseModel, Field

class AnalysisResult(BaseModel):
    """分析结果结构化输出"""
    root_cause: str = Field(description="根本原因分析")
    evidence: List[str] = Field(description="支持证据列表")
    timeline: List[str] = Field(description="问题时间线")
    recommendations: List[str] = Field(description="解决建议")
    confidence: float = Field(description="分析置信度", ge=0.0, le=1.0)

# Agent配置结构化输出
analysis_agent = LlmAgent(
    model=deepseek_model,
    instruction=ANALYSIS_INSTRUCTION,
    output_schema=AnalysisResult,
    tools=[search_tool, ragflow_tool]
)
```

### 多模型协作模式

#### 1. 角色分工设计
```python
# DeepSeek-V3-0324：对话和工具调用
chat_agent = LlmAgent(
    model=DeepSeekLiteLlm(model="openai/DeepSeek-V3-0324"),
    name="chat_coordinator", 
    instruction="协调整个分析流程，调用工具获取信息",
    tools=[all_tools]
)

# DeepSeek-R1：深度推理分析
reasoning_agent = LlmAgent(
    model=DeepSeekLiteLlm(model="openai/DeepSeek-R1"),
    name="reasoning_analyzer",
    instruction=REASONING_INSTRUCTION,
    disallow_transfer_to_peers=True  # 专注推理任务
)

# 顺序执行模式
root_agent = SequentialAgent(
    name="log_analysis_system",
    sub_agents=[chat_agent, reasoning_agent]
)
```

## 🔍 配置管理架构

### Pydantic配置模式

#### 标准配置结构
```python
from pydantic import BaseSettings, Field, validator

class DeepSeekConfig(BaseSettings):
    """DeepSeek模型配置 - 遵循ADK模式"""
    api_base: str = Field(..., env="DEEPSEEK_API_BASE")
    api_key: str = Field(..., env="DEEPSEEK_API_KEY")
    model: str = Field("openai/DeepSeek-V3-0324", env="DEEPSEEK_MODEL")
    temperature: float = Field(0.6, env="DEEPSEEK_TEMPERATURE")
    max_tokens: int = Field(4096, env="DEEPSEEK_MAX_TOKENS")
    
    @validator('temperature')
    def validate_temperature(cls, v):
        if not 0.0 <= v <= 2.0:
            raise ValueError('temperature必须在0.0-2.0之间')
        return v

class AppConfig(BaseSettings):
    """应用主配置"""
    deepseek: DeepSeekConfig = Field(default_factory=DeepSeekConfig)
    ragflow: RAGFlowConfig = Field(default_factory=RAGFlowConfig)
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
```

## 🏭 异步处理架构

### 异步执行模式

#### 1. 事件驱动处理
```python
async def execute_analysis_workflow(self, problem: str) -> AnalysisReport:
    """异步分析工作流 - 遵循ADK事件模式"""
    
    # 创建上下文
    session = await self.session_service.create_session(
        app_name="log-analyzer",
        user_id="system"
    )
    
    # 异步事件流处理
    async for event in self.runner.run_async(
        user_id=session.user_id,
        session_id=session.id,
        new_message=types.Content(
            role="user",
            parts=[types.Part.from_text(problem)]
        )
    ):
        # 处理事件
        if event.content:
            yield event
        
        # 状态更新
        if event.actions.state_delta:
            await self.update_analysis_state(event.actions.state_delta)
```

#### 2. 批量并行处理
```python
async def parallel_log_analysis(self, log_files: List[str]) -> List[AnalysisResult]:
    """并行分析多个日志文件"""
    
    tasks = []
    semaphore = asyncio.Semaphore(self.config.parallel_limit)
    
    async def analyze_single_file(file_path: str):
        async with semaphore:
            return await self.analyze_log_file(file_path)
    
    for file_path in log_files:
        task = asyncio.create_task(analyze_single_file(file_path))
        tasks.append(task)
    
    return await asyncio.gather(*tasks, return_exceptions=True)
```

## 🔧 错误处理与兼容性

### 异常处理体系

#### 1. 分层异常设计
```python
class LogAnalyzerException(Exception):
    """日志分析器基础异常"""
    pass

class DeepSeekException(LogAnalyzerException):
    """DeepSeek模型相关异常"""
    pass

class RAGFlowException(LogAnalyzerException):
    """RAGFlow服务异常"""
    pass

# 使用模式
try:
    result = await self.deepseek_client.analyze(prompt)
except DeepSeekException as e:
    logger.warning(f"DeepSeek分析失败，使用降级方案: {e}")
    result = await self.fallback_analysis(prompt)
```

#### 2. 优雅降级机制
```python
class ComponentManager:
    """组件管理器 - 支持优雅降级"""
    
    def __init__(self, config: AppConfig):
        self.components = {}
        self._init_components_with_fallback(config)
    
    def _init_components_with_fallback(self, config):
        """带降级的组件初始化"""
        try:
            self.components['deepseek'] = DeepSeekClient(config.deepseek)
        except Exception as e:
            logger.warning(f"DeepSeek初始化失败，使用Mock: {e}")
            self.components['deepseek'] = MockDeepSeekClient()
        
        try:
            self.components['ragflow'] = RAGFlowClient(config.ragflow)
        except Exception as e:
            logger.warning(f"RAGFlow初始化失败，使用Mock: {e}")
            self.components['ragflow'] = MockRAGFlowClient()
```

### DeepSeek-ADK兼容性

#### ADK集成修复模式
```python
from google.adk.models.lite_llm import LiteLlm

class DeepSeekLiteLlm(LiteLlm):
    """DeepSeek兼容性修复 - ADK集成"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
    
    async def generate_content_async(self, llm_request: LlmRequest, stream: bool = False):
        """修复role='developer'兼容性问题"""
        
        # Monkey patch修复
        original_get_completion_inputs = None
        try:
            from google.adk.models import lite_llm as litellm_module
            original_get_completion_inputs = litellm_module._get_completion_inputs
            
            def fixed_get_completion_inputs(request):
                messages, tools, response_format, extra = original_get_completion_inputs(request)
                # 修复developer角色
                messages = self._fix_developer_role(messages)
                return messages, tools, response_format, extra
            
            litellm_module._get_completion_inputs = fixed_get_completion_inputs
            
            async for response in super().generate_content_async(llm_request, stream):
                yield response
                
        finally:
            if original_get_completion_inputs:
                litellm_module._get_completion_inputs = original_get_completion_inputs
    
    def _fix_developer_role(self, messages: list) -> list:
        """将developer角色转换为system角色"""
        for message in messages:
            if hasattr(message, 'role') and message.role == 'developer':
                message.role = 'system'
        return messages
```

## 📚 官方文档和资源参考

### 关键参考文档

#### 1. Google ADK官方资源
- **[ADK官方文档](https://google.github.io/adk-docs/)** - 完整架构和API文档
- **[ADK Python源码](https://github.com/google/adk-python)** - 源码级架构参考
- **[LiteLLM集成指南](https://google.github.io/adk-docs/python/models/lite-llm/)** - 多模型集成

#### 2. DeepSeek模型资源
- **[DeepSeek API文档](https://api-docs.deepseek.com/zh-cn/)** - API规范和使用指南
- **[DeepSeek Function Calling](https://api-docs.deepseek.com/guides/function_calling)** - 工具调用文档

#### 3. 第三方集成
- **[LiteLLM文档](https://docs.litellm.ai/)** - 多模型适配框架
- **[RAGFlow文档](https://ragflow.io/docs/)** - 知识库集成指南

### 官方示例分析

#### ADK官方示例模式
```python
# 参考：adk-samples/python/agents/software-bug-assistant
# 标准Agent定义模式
root_agent = Agent(
    model="gemini-2.5-flash",  # 官方推荐Gemini
    name="software_assistant", 
    instruction=agent_instruction,
    tools=[get_current_date, search_tool, langchain_tool, *toolbox_tools]
)

# 我们的DeepSeek适配
root_agent = Agent(
    model=DeepSeekLiteLlm(model="openai/DeepSeek-V3-0324"),  # 自定义模型
    name="log_analyzer",
    instruction=analysis_instruction,
    tools=[ragflow_tool, log_search_tool, file_manager_tool]
)
```

## 🚀 开发工作流规范

### 三阶段开发流程

#### 阶段一：【分析问题】
- 理解需求本质，思考系统性影响
- 分析现有架构，识别重复代码
- 评估技术债务和长期维护成本

#### 阶段二：【细化方案】
- 列出需要变更的文件（编辑现有 > 创建新文件）
- 描述每个文件的具体变化
- 确保方案符合DRY和KISS原则

#### 阶段三：【执行方案】
- 实际代码实现，遵循架构模式
- 保持代码简洁，避免硬编码
- 实现MVP功能，迭代完善

### 强制检查点
- [ ] 声明当前开发阶段
- [ ] 行为符合阶段要求
- [ ] 获得明确同意后才切换阶段
- [ ] 识别并消除重复代码
- [ ] 评估长期维护成本

## 🎯 质量保证机制

### MVP导向开发
1. **核心功能优先**：先实现基本的日志分析能力
2. **快速验证可行性**：确保DeepSeek和RAGFlow集成正常
3. **迭代完善**：在MVP基础上逐步增强功能

### 代码质量标准
1. **配置外部化**：所有配置通过Pydantic管理
2. **异常处理完善**：分层异常体系和降级机制
3. **异步处理优化**：合理使用async/await和并行处理
4. **工具标准化**：遵循ADK工具架构模式

### 长远考虑
1. **技术债务评估**：每次变更都要考虑维护成本
2. **扩展性设计**：支持新的LLM模型和工具集成
3. **性能优化**：大文件处理和并发能力

---

## 总结

本开发标准基于Google ADK官方架构、DeepSeek模型特性和我们的项目实践，形成了完整的开发规范体系。重点强调：

1. **遵循ADK官方架构模式**：Agent层次化、工具标准化、事件驱动
2. **DeepSeek兼容性解决方案**：role修复、多模型协作
3. **企业级质量标准**：配置管理、异常处理、异步架构
4. **可维护的开发流程**：三阶段工作流、质量检查点

这个规范既保证了技术先进性，又确保了长期维护性和扩展性。 