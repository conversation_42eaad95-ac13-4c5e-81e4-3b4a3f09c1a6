#!/usr/bin/env python3
"""
通用后台日志分析CLI

完全通用的后台系统日志分析工具，不假设任何特定业务模块。
支持任何类型的后台系统日志分析。
"""

import click
import json
import sys
import asyncio
import time
from pathlib import Path

# 添加src到Python路径
sys.path.insert(0, str(Path(__file__).parent))

# 导入核心模块
from config.simple_config import get_config
from core.agent import LogAnalysisAgent

@click.group()
def cli():
    """通用后台日志分析工具"""
    pass

@cli.command()
@click.argument('problem')
@click.option('--output', default='console', type=click.Choice(['console', 'json', 'file']), help='输出格式')
@click.option('--verbose', is_flag=True, help='详细输出')
def analyze(problem, output, verbose):
    """执行日志分析
    
    示例:
        python3 src/cli.py analyze "系统连接超时问题分析"
        python3 src/cli.py analyze "数据库异常错误" --output json
        python3 src/cli.py analyze "服务启动失败问题"
    """
    print(f"通用日志分析器启动")
    print(f"分析问题: {problem}")
    print("-" * 60)
    
    # 运行异步分析
    result = asyncio.run(_execute_analysis(problem, verbose))
    
    # 输出结果
    _output_result(result, output, problem)

async def _execute_analysis(problem: str, verbose: bool):
    """执行异步分析"""
    start_time = time.time()
    
    try:
        # 验证配置
        config = get_config()
        validation_errors = config.validate_required_settings()
        if validation_errors:
            print("配置验证失败:")
            for error in validation_errors:
                print(f"  - {error}")
            return None
        
        # 创建分析代理
        print("初始化通用分析代理...")
        agent = LogAnalysisAgent(config)
        
        # 执行5阶段分析
        print("执行5阶段智能分析流程...")
        result = await agent.analyze_async(problem)
        
        analysis_time = time.time() - start_time
        print(f"分析完成，耗时: {analysis_time:.1f}秒")
        
        return result
        
    except Exception as e:
        print(f"分析失败: {e}")
        if verbose:
            import traceback
            traceback.print_exc()
        raise

def _output_result(result, output_format: str, problem: str):
    """输出分析结果"""
    if output_format == 'json':
        result_dict = _to_dict(result)
        print(json.dumps(result_dict, indent=2, ensure_ascii=False))
        
    elif output_format == 'file':
        result_dict = _to_dict(result)
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        filename = f"analysis_result_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(result_dict, f, indent=2, ensure_ascii=False)
        print(f"结果已保存到: {filename}")
        
    else:  # console
        _print_console_result(result, problem)

def _to_dict(result) -> dict:
    """将分析结果转换为字典"""
    if result is None:
        return {"status": "error", "message": "分析未返回结果"}
    
    if isinstance(result, dict):
        return result
    
    # 处理AnalysisReport对象
    try:
        return {
            "analysis_completed": True,
            "summary": getattr(result, 'summary', '分析完成'),
            "confidence": getattr(result, 'confidence', 0.0),
            "findings": getattr(result, 'findings', []),
            "recommendations": getattr(result, 'recommendations', []),
            "execution_time": getattr(result, 'execution_time', 0.0),
            "total_files_analyzed": getattr(result, 'total_files_analyzed', 0)
        }
    except Exception:
        return {"status": "error", "message": "结果转换失败"}

def _print_console_result(result, problem: str):
    """控制台格式化输出"""
    print("\n" + "=" * 60)
    print("分析结果")
    print("=" * 60)
    
    if result is None:
        print("分析未返回结果")
        return
    
    print(f"问题: {problem}")
    
    # 显示基本信息
    if hasattr(result, 'summary'):
        print(f"摘要: {result.summary}")
    
    if hasattr(result, 'confidence'):
        print(f"置信度: {result.confidence:.1%}")
    
    # 显示发现
    if hasattr(result, 'findings') and result.findings:
        print(f"\n主要发现:")
        for i, finding in enumerate(result.findings[:5], 1):
            if hasattr(finding, 'description'):
                print(f"  {i}. {finding.description}")
            else:
                print(f"  {i}. {finding}")
    
    # 显示建议
    if hasattr(result, 'recommendations') and result.recommendations:
        print(f"\n建议措施:")
        for i, rec in enumerate(result.recommendations[:5], 1):
            print(f"  {i}. {rec}")
    
    print("\n" + "=" * 60)

@cli.command()
def config():
    """显示当前配置"""
    try:
        config = get_config()
        
        print("当前配置:")
        config_dict = config.to_dict()
        
        print(f"  应用名称: {config_dict['app_name']}")
        print(f"  版本: {config_dict['version']}")
        print(f"  调试模式: {config_dict['debug']}")
        print(f"  日志目录: {config_dict['log_analysis']['log_directory']}")
        
        # DeepSeek配置
        if config_dict['deepseek']:
            print(f"  DeepSeek API: 已配置")
            print(f"    模型: {config_dict['deepseek']['model']}")
            print(f"    温度: {config_dict['deepseek']['temperature']}")
        
        # RAGFlow配置
        if config_dict['ragflow']:
            print(f"  RAGFlow API: 已配置")
            print(f"    助手名称: {config_dict['ragflow']['assistant_name']}")
        
    except Exception as e:
        print(f"配置错误: {e}")

@cli.command()
def check():
    """检查系统状态"""
    print("检查系统组件...")
    
    # 检查配置
    try:
        config = get_config()
        validation_errors = config.validate_required_settings()
        if validation_errors:
            print("配置验证失败:")
            for error in validation_errors:
                print(f"  - {error}")
            return
        else:
            print("配置加载成功")
    except Exception as e:
        print(f"配置加载失败: {e}")
        return
    
    # 检查Agent
    try:
        agent = LogAnalysisAgent(config)
        print("分析代理创建成功")
    except Exception as e:
        print(f"分析代理创建失败: {e}")
        return
    
    print("系统检查完成，所有组件正常")

if __name__ == '__main__':
    cli() 