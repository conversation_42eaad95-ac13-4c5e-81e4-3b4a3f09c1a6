"""
智能日志分析器核心模块

提供基础的数据模型、Agent控制器和会话管理功能
"""

from .models import (
    AnalysisState,
    FindingType,
    SearchQuery,
    SearchResult,
    Evidence,
    Finding,
    AnalysisContext,
    AnalysisReport
)

from .agent import LogAnalysisAgent
from .exceptions import (
    LogAnalyzerException,
    ConfigurationError,
    DeepSeekError,
    RAGFlowError,
    LogSearchError,
    FileProcessingError,
    NetworkError
)

__all__ = [
    # 数据模型
    'AnalysisState',
    'FindingType', 
    'SearchQuery',
    'SearchResult',
    'Evidence',
    'Finding',
    'AnalysisContext',
    'AnalysisReport',
    
    # 核心Agent
    'LogAnalysisAgent',
    
    # 异常类
    'LogAnalyzerException',
    'ConfigurationError',
    'DeepSeekError',
    'RAGFlowError',
    'LogSearchError',
    'FileProcessingError',
    'NetworkError'
]

__version__ = "1.0.0" 