"""
智能日志分析器数据模型

定义所有核心数据结构，包括分析上下文、搜索结果、发现和报告等
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any, Union
from datetime import datetime, timedelta
from enum import Enum
import uuid

class AnalysisState(Enum):
    """分析状态枚举"""
    INITIALIZED = "initialized"
    QUERYING_METHOD = "querying_method"
    DECOMPRESSING = "decompressing"
    SEARCHING = "searching"
    READING = "reading"
    ANALYZING = "analyzing"


class FindingType(Enum):
    """发现类型枚举"""
    ROOT_CAUSE = "ROOT_CAUSE"
    EVIDENCE = "evidence"
    TIMELINE = "timeline"
    PATTERN = "PATTERN"
    ANOMALY = "anomaly"
    FREQUENCY_ANOMALY = "FREQUENCY_ANOMALY"
    TEMPORAL_ANOMALY = "TEMPORAL_ANOMALY"
    SEQUENCE = "SEQUENCE"
    RECOMMENDATION = "recommendation"
    WARNING = "warning"
    INFO = "info"

class LogLevel(Enum):
    """日志级别枚举"""
    TRACE = "TRACE"
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARN = "WARN"
    ERROR = "ERROR"
    FATAL = "FATAL"

@dataclass
class AnalysisContext:
    """分析上下文"""
    problem_description: str
    start_time: datetime = field(default_factory=datetime.now)
    analysis_method: str = ""
    analysis_keywords: List[str] = field(default_factory=list)
    prepared_files: List[str] = field(default_factory=list)


class ConfidenceLevel(Enum):
    """置信度级别"""
    VERY_LOW = "very_low"     # 0.0 - 0.2
    LOW = "low"               # 0.2 - 0.4
    MEDIUM = "medium"         # 0.4 - 0.6
    HIGH = "high"             # 0.6 - 0.8
    VERY_HIGH = "very_high"   # 0.8 - 1.0

@dataclass
class TimeRange:
    """时间范围模型"""
    start: datetime
    end: datetime
    
    def contains(self, timestamp: datetime) -> bool:
        """检查时间戳是否在范围内"""
        return self.start <= timestamp <= self.end
    
    def duration_seconds(self) -> float:
        """获取时间范围的秒数"""
        return (self.end - self.start).total_seconds()

@dataclass
class SearchQuery:
    """搜索查询模型"""
    keywords: List[str]
    time_range: Optional[TimeRange] = None
    file_pattern: Optional[str] = None
    log_level: Optional[LogLevel] = None
    context_lines: int = 10
    max_results: int = 100
    case_sensitive: bool = False
    regex_mode: bool = False
    
    def __post_init__(self):
        # 清理和验证关键词
        self.keywords = [kw.strip() for kw in self.keywords if kw.strip()]
        if not self.keywords:
            raise ValueError("搜索关键词不能为空")

@dataclass
class SearchResult:
    """搜索结果模型"""
    file_path: str
    line_number: int
    matched_content: str
    keyword: str = ""
    context_before: List[str] = field(default_factory=list)
    context_after: List[str] = field(default_factory=list)
    confidence_score: float = 1.0
    timestamp: Optional[datetime] = None
    byte_offset: int = 0
    log_level: Optional[LogLevel] = None
    matched_keywords: List[str] = field(default_factory=list)
    
    def get_full_context(self) -> str:
        """获取完整上下文内容"""
        lines = self.context_before + [self.matched_content] + self.context_after
        return '\n'.join(lines)
    
    def get_confidence_level(self) -> ConfidenceLevel:
        """获取置信度级别"""
        if self.confidence_score >= 0.8:
            return ConfidenceLevel.VERY_HIGH
        elif self.confidence_score >= 0.6:
            return ConfidenceLevel.HIGH
        elif self.confidence_score >= 0.4:
            return ConfidenceLevel.MEDIUM
        elif self.confidence_score >= 0.2:
            return ConfidenceLevel.LOW
        else:
            return ConfidenceLevel.VERY_LOW

@dataclass
class Evidence:
    """证据模型"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    type: str = "log_evidence"
    title: str = ""
    description: str = ""
    source_file: str = ""
    line_number: int = 0
    content: str = ""
    timestamp: Optional[datetime] = None
    confidence: float = 1.0
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        if not self.title:
            self.title = f"证据_{self.id[:8]}"

@dataclass
class Finding:
    """分析发现模型"""
    type: FindingType
    title: str
    description: str
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    evidence: List[Evidence] = field(default_factory=list)
    confidence: float = 0.0
    severity: str = "medium"  # low, medium, high, critical
    timestamp: datetime = field(default_factory=datetime.now)
    impact: Optional[str] = None
    
    def add_evidence(self, evidence: Evidence):
        """添加证据"""
        self.evidence.append(evidence)
        # 重新计算置信度
        self._recalculate_confidence()
    
    def _recalculate_confidence(self):
        """重新计算置信度"""
        if not self.evidence:
            self.confidence = 0.0
        else:
            # 简单的置信度计算：证据置信度的加权平均
            total_confidence = sum(e.confidence for e in self.evidence)
            self.confidence = min(1.0, total_confidence / len(self.evidence))

@dataclass
class AnalysisMethod:
    """分析方法模型"""
    name: str
    description: str
    steps: List[str] = field(default_factory=list)
    keywords: List[str] = field(default_factory=list)
    confidence: float = 1.0
    source: str = "ragflow"  # ragflow, builtin, generated
    
class AnalysisProgress:
    """分析进度追踪"""
    
    def __init__(self):
        self.total_steps = 0
        self.completed_steps = 0
        self.current_step = ""
        self.start_time = datetime.now()
        self.estimated_completion: Optional[datetime] = None
    
    def update(self, step_name: str, completed: int = None, total: int = None):
        """更新进度"""
        self.current_step = step_name
        if completed is not None:
            self.completed_steps = completed
        if total is not None:
            self.total_steps = total
        
        # 估算完成时间
        if self.total_steps > 0 and self.completed_steps > 0:
            elapsed = (datetime.now() - self.start_time).total_seconds()
            estimated_total = elapsed * (self.total_steps / self.completed_steps)
            self.estimated_completion = self.start_time + timedelta(seconds=estimated_total)
    
    def get_progress_percentage(self) -> float:
        """获取进度百分比"""
        if self.total_steps == 0:
            return 0.0
        return min(100.0, (self.completed_steps / self.total_steps) * 100)

@dataclass
class AnalysisContext:
    """分析上下文模型"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    problem: str = ""  # 主要问题描述字段
    problem_description: str = ""  # 保持兼容性
    log_paths: List[str] = field(default_factory=list)  # 日志路径列表
    analysis_method: Optional[str] = None  # 分析方法文本
    prepared_files: List[str] = field(default_factory=list)  # 准备好的文件列表
    keywords_searched: List[str] = field(default_factory=list)  # 搜索关键词
    evaluation_notes: str = ""  # 评估备注
    current_step: str = "initialized"
    search_results: List[SearchResult] = field(default_factory=list)
    findings: List[Finding] = field(default_factory=list)
    analysis_state: AnalysisState = AnalysisState.INITIALIZED
    session_id: str = ""
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    progress: AnalysisProgress = field(default_factory=AnalysisProgress)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """初始化后处理"""
        # 如果problem为空但problem_description不为空，则同步
        if not self.problem and self.problem_description:
            self.problem = self.problem_description
        elif self.problem and not self.problem_description:
            self.problem_description = self.problem
    
    def update_state(self, new_state: AnalysisState, step_description: str = ""):
        """更新分析状态"""
        self.analysis_state = new_state
        self.current_step = step_description or new_state.value
        self.updated_at = datetime.now()
        
    def add_finding(self, finding: Finding):
        """添加发现"""
        self.findings.append(finding)
        self.updated_at = datetime.now()
    
    def get_evidence_count(self) -> int:
        """获取证据总数"""
        return sum(len(finding.evidence) for finding in self.findings)
    
    def get_confidence_score(self) -> float:
        """获取整体置信度分数"""
        if not self.findings:
            return 0.0
        total_confidence = sum(f.confidence for f in self.findings)
        return min(1.0, total_confidence / len(self.findings))

@dataclass
class TimelineEvent:
    """时间线事件模型"""
    timestamp: datetime
    event_type: str
    title: str = ""
    description: str = ""
    source_file: str = ""
    source_files: List[str] = field(default_factory=list)
    line_number: int = 0
    severity: str = "info"
    confidence: float = 1.0
    details: Dict[str, Any] = field(default_factory=dict)

@dataclass
class AnalysisReport:
    """分析报告模型"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    context_id: str = ""
    problem: str = ""
    summary: str = ""
    root_cause: Optional[str] = None
    timeline: List[TimelineEvent] = field(default_factory=list)
    findings: List[Finding] = field(default_factory=list)
    evidence: List[Evidence] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    confidence_score: float = 0.0
    analysis_duration: float = 0.0
    files_analyzed: List[str] = field(default_factory=list)
    keywords_searched: List[str] = field(default_factory=list)
    generated_at: datetime = field(default_factory=datetime.now)
    analyst: str = "intelligent_log_analyzer"
    version: str = "1.0.0"
    
    # 新增字段 - 支持报告生成器
    performance_info: Dict[str, Any] = field(default_factory=dict)
    problem_classification: Dict[str, Any] = field(default_factory=dict)
    total_findings: int = 0
    critical_findings: int = 0
    high_findings: int = 0
    analysis_timestamp: Optional[datetime] = None
    source_files: List[str] = field(default_factory=list)
    
    def to_markdown(self) -> str:
        """生成Markdown格式的报告"""
        lines = []
        lines.append(f"# 日志分析报告")
        lines.append(f"**报告ID**: {self.id}")
        lines.append(f"**生成时间**: {self.generated_at.strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append(f"**分析时长**: {self.analysis_duration:.2f}秒")
        lines.append(f"**置信度**: {self.confidence_score:.2%}")
        lines.append("")
        
        lines.append("## 问题描述")
        lines.append(self.problem)
        lines.append("")
        
        lines.append("## 分析摘要")
        lines.append(self.summary)
        lines.append("")
        
        if self.root_cause:
            lines.append("## 根本原因")
            lines.append(self.root_cause)
            lines.append("")
        
        if self.findings:
            lines.append("## 关键发现")
            for i, finding in enumerate(self.findings, 1):
                lines.append(f"### {i}. {finding.title}")
                lines.append(f"**类型**: {finding.type.value}")
                lines.append(f"**严重程度**: {finding.severity}")
                lines.append(f"**置信度**: {finding.confidence:.2%}")
                lines.append(f"**描述**: {finding.description}")
                lines.append("")
        
        if self.timeline:
            lines.append("## 时间线")
            for event in sorted(self.timeline, key=lambda x: x.timestamp):
                time_str = event.timestamp.strftime('%Y-%m-%d %H:%M:%S')
                lines.append(f"- **{time_str}**: {event.title or event.description}")
            lines.append("")
        
        if self.recommendations:
            lines.append("## 建议措施")
            for i, rec in enumerate(self.recommendations, 1):
                lines.append(f"{i}. {rec}")
            lines.append("")
        
        lines.append("## 分析统计")
        lines.append(f"- 分析文件数: {len(self.files_analyzed)}")
        lines.append(f"- 搜索关键词: {', '.join(self.keywords_searched)}")
        lines.append(f"- 发现数量: {len(self.findings)}")
        lines.append(f"- 证据数量: {len(self.evidence)}")
        lines.append(f"- 严重问题: {self.critical_findings} 个")
        lines.append(f"- 高优先级问题: {self.high_findings} 个")
        
        return '\n'.join(lines)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'context_id': self.context_id,
            'problem': self.problem,
            'summary': self.summary,
            'root_cause': self.root_cause,
            'timeline': [
                {
                    'timestamp': event.timestamp.isoformat(),
                    'event_type': event.event_type,
                    'title': event.title,
                    'description': event.description,
                    'source_file': event.source_file,
                    'source_files': event.source_files,
                    'line_number': event.line_number,
                    'severity': event.severity,
                    'confidence': event.confidence,
                    'details': event.details
                }
                for event in self.timeline
            ],
            'findings': [
                {
                    'id': finding.id,
                    'type': finding.type.value,
                    'title': finding.title,
                    'description': finding.description,
                    'confidence': finding.confidence,
                    'severity': finding.severity,
                    'evidence_count': len(finding.evidence)
                }
                for finding in self.findings
            ],
            'recommendations': self.recommendations,
            'confidence_score': self.confidence_score,
            'analysis_duration': self.analysis_duration,
            'files_analyzed': self.files_analyzed,
            'keywords_searched': self.keywords_searched,
            'generated_at': self.generated_at.isoformat(),
            'analyst': self.analyst,
            'version': self.version,
            'performance_info': self.performance_info,
            'problem_classification': self.problem_classification,
            'total_findings': self.total_findings,
            'critical_findings': self.critical_findings,
            'high_findings': self.high_findings
        }

# 辅助函数
def create_analysis_context(problem: str, **kwargs) -> AnalysisContext:
    """创建分析上下文的辅助函数"""
    context = AnalysisContext(problem_description=problem)
    
    # 设置可选参数
    for key, value in kwargs.items():
        if hasattr(context, key):
            setattr(context, key, value)
    
    return context


def calculate_confidence_level(score: float) -> ConfidenceLevel:
    """根据置信度分数计算置信度级别"""
    if score >= 0.8:
        return ConfidenceLevel.VERY_HIGH
    elif score >= 0.6:
        return ConfidenceLevel.HIGH
    elif score >= 0.4:
        return ConfidenceLevel.MEDIUM
    elif score >= 0.2:
        return ConfidenceLevel.LOW
    else:
        return ConfidenceLevel.VERY_LOW 