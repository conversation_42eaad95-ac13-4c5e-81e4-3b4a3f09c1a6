---
description: Intelligent Log Analyzer project structure and patterns
---

# Intelligent Log Analyzer Project Guide

## Project Structure
The main entry point is [src/cli.py](mdc:src/cli.py), which provides CLI commands for log analysis.

Core configuration is managed in [src/config/settings.py](mdc:src/config/settings.py) using Pydantic BaseSettings.

The main analysis logic is in [src/core/agent.py](mdc:src/core/agent.py) - the LogAnalysisAgent class.

## Key Components

### Configuration Management
- [src/config/settings.py](mdc:src/config/settings.py) - Centralized configuration
- [config.example.env](mdc:config.example.env) - Configuration template
- Environment variables override .env file settings

### Core Analysis Engine
- [src/core/agent.py](mdc:src/core/agent.py) - Main LogAnalysisAgent
- [src/core/models.py](mdc:src/core/models.py) - Data models and structures
- [src/core/exceptions.py](mdc:src/core/exceptions.py) - Custom exception handling

### Tool Integration
- [src/tools/ragflow_client.py](mdc:src/tools/ragflow_client.py) - RAGFlow knowledge base integration
- [src/tools/log_search.py](mdc:src/tools/log_search.py) - Log searching and file management
- [src/agents/deepseek_llm.py](mdc:src/agents/deepseek_llm.py) - DeepSeek model integration

## Architecture Patterns

### Dependency Management
- Optional imports with fallback mechanisms
- Graceful degradation when dependencies unavailable
- Mock classes for development without external services

### Integration Patterns
- DeepSeek role compatibility fixes for ADK integration
- RAGFlow API abstraction with assistant management
- Asynchronous log processing with context management

### Error Handling
- Custom exception hierarchy for different failure types
- Validation at configuration load time
- Fallback strategies for external service failures

## Development Guidelines

### Adding New Features
1. Add configuration options to [src/config/settings.py](mdc:src/config/settings.py)
2. Create tool modules in src/tools/ for external integrations
3. Update [src/core/agent.py](mdc:src/core/agent.py) to orchestrate new functionality
4. Add CLI commands to [src/cli.py](mdc:src/cli.py) if needed

### Testing Integration
- Test configuration loading separately
- Use mock services for external dependencies
- Create integration tests for core workflows
- Validate error handling paths
