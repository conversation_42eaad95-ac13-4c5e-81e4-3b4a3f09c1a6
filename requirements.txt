# 智能日志分析器依赖包

# 核心框架
google-adk[dev]==1.0.0  # Google ADK框架
pydantic==2.5.0         # 数据验证和设置管理
pydantic-settings==2.1.0  # Pydantic设置管理
fastapi==0.104.1        # Web API框架
uvicorn==0.24.0         # ASGI服务器

# LLM相关
litellm==1.71.2         # LLM接口库
openai==1.51.0          # OpenAI SDK（LiteLLM需要）
httpx==0.25.2           # HTTP客户端

# 数据处理
pandas==2.1.4           # 数据分析
numpy==1.24.4           # 数值计算

# 异步和并发
asyncio                 # 异步编程（Python内置）
aiofiles==23.2.1        # 异步文件操作
aiocache==0.12.2        # 异步缓存

# 数据库（可选）
sqlalchemy==2.0.23      # ORM
alembic==1.13.0         # 数据库迁移
aiosqlite==0.19.0       # 异步SQLite

# 日志和监控
structlog==23.2.0       # 结构化日志
prometheus-client==0.19.0  # 监控指标

# 工具和实用程序
click==8.1.7            # CLI工具
rich==13.7.0            # 美化终端输出
tqdm==4.66.1            # 进度条
python-dotenv==1.0.0    # 环境变量管理

# 文件处理
pathlib                 # 路径处理（Python内置）
shutil                  # 文件操作（Python内置）
zipfile                 # 压缩文件（Python内置）
gzip                    # Gzip压缩（Python内置）

# 时间处理
python-dateutil==2.8.2  # 日期解析
pytz==2023.3.post1      # 时区处理

# 开发和测试工具
pytest==7.4.3          # 测试框架
pytest-asyncio==0.23.0  # 异步测试
pytest-mock==3.12.0    # Mock工具
pytest-cov==4.1.0      # 测试覆盖率
black==23.12.0          # 代码格式化
isort==5.13.0           # 导入排序
flake8==6.1.0           # 代码检查
mypy==1.8.0             # 类型检查

# 安全
cryptography==41.0.8    # 加密库

# 可选的前端界面
streamlit==1.29.0       # Web界面（可选）
gradio==4.7.1           # AI界面（可选）

# 日志分析特定工具
regex==2023.10.3        # 正则表达式增强
chardet==5.2.0          # 字符编码检测 