#!/usr/bin/env python3
"""
智能日志分析器初始化脚本

用于设置项目环境、检查依赖、创建必要的目录和文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
from typing import List, Tu<PERSON>

def check_python_version() -> bool:
    """检查Python版本"""
    if sys.version_info < (3, 9):
        print("❌ Python 3.9+ 是必需的")
        print(f"当前版本: {sys.version}")
        return False
    print(f"✅ Python版本检查通过: {sys.version}")
    return True

def check_command_availability(command: str) -> bool:
    """检查命令是否可用"""
    return shutil.which(command) is not None

def check_dependencies() -> List[Tuple[str, bool, str]]:
    """检查系统依赖"""
    deps = [
        ("ripgrep", "rg", "高性能文本搜索工具"),
        ("git", "git", "版本控制系统"),
    ]
    
    results = []
    for name, command, description in deps:
        available = check_command_availability(command)
        results.append((name, available, description))
        status = "✅" if available else "❌"
        print(f"{status} {name}: {description}")
    
    return results

def create_directories():
    """创建必要的目录"""
    directories = [
        "data/logs",
        "data/knowledge_base", 
        "data/reports",
        "logs",
        "temp",
        "src/core",
        "src/tools",
        "src/agents", 
        "src/utils",
        "src/config",
        "tests/unit",
        "tests/integration",
    ]
    
    print("\n📁 创建目录结构...")
    for directory in directories:
        path = Path(directory)
        if not path.exists():
            path.mkdir(parents=True, exist_ok=True)
            print(f"✅ 创建目录: {directory}")
        else:
            print(f"📁 目录已存在: {directory}")

def create_init_files():
    """创建__init__.py文件"""
    init_files = [
        "src/__init__.py",
        "src/tools/__init__.py", 
        "src/agents/__init__.py",
        "src/utils/__init__.py",
        "src/config/__init__.py",
        "tests/__init__.py",
        "tests/unit/__init__.py",
        "tests/integration/__init__.py",
    ]
    
    print("\n📄 创建__init__.py文件...")
    for file_path in init_files:
        path = Path(file_path)
        if not path.exists():
            path.touch()
            print(f"✅ 创建文件: {file_path}")
        else:
            print(f"📄 文件已存在: {file_path}")

def create_config_file():
    """创建默认配置文件"""
    env_file = Path(".env")
    example_file = Path("config.example.env")
    
    if not env_file.exists() and example_file.exists():
        shutil.copy(example_file, env_file)
        print("✅ 创建 .env 配置文件")
        print("⚠️  请编辑 .env 文件，填入实际的配置值")
    else:
        print("📄 .env 文件已存在")

def install_pre_commit():
    """安装pre-commit钩子"""
    try:
        # 检查是否在git仓库中
        result = subprocess.run(
            ["git", "rev-parse", "--git-dir"], 
            capture_output=True, 
            text=True
        )
        
        if result.returncode != 0:
            print("⚠️  未检测到Git仓库，跳过pre-commit安装")
            return
        
        # 安装pre-commit
        subprocess.run([sys.executable, "-m", "pip", "install", "pre-commit"], 
                      check=True, capture_output=True)
        
        # 创建.pre-commit-config.yaml
        pre_commit_config = """
repos:
  - repo: https://github.com/psf/black
    rev: 23.12.0
    hooks:
      - id: black
        language_version: python3
        
  - repo: https://github.com/pycqa/isort
    rev: 5.13.0
    hooks:
      - id: isort
        
  - repo: https://github.com/pycqa/flake8
    rev: 6.1.0
    hooks:
      - id: flake8
        args: [--max-line-length=88, --extend-ignore=E203,W503]
        
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.8.0
    hooks:
      - id: mypy
        additional_dependencies: [pydantic, types-requests]
"""
        
        config_file = Path(".pre-commit-config.yaml")
        if not config_file.exists():
            config_file.write_text(pre_commit_config.strip())
            print("✅ 创建 .pre-commit-config.yaml")
        
        # 安装pre-commit钩子
        subprocess.run(["pre-commit", "install"], check=True, capture_output=True)
        print("✅ 安装 pre-commit 钩子")
        
    except subprocess.CalledProcessError as e:
        print(f"⚠️  pre-commit安装失败: {e}")
    except Exception as e:
        print(f"⚠️  pre-commit设置错误: {e}")

def create_sample_files():
    """创建示例文件"""
    
    # 创建示例分析配置
    sample_analysis = """# 示例分析配置

## OAuth2 Token失效问题

### 关键词
- auth:token:app
- TokenCache
- Redis
- accessToken
- refreshToken

### 搜索策略
1. 搜索token ID
2. 查找相关时间戳
3. 分析并发请求
4. 检查Redis操作日志

### 常见原因
- 并发请求导致覆盖
- Token过期
- Redis连接问题
- 客户端重复登录
"""
    
    sample_file = Path("data/knowledge_base/oauth2_analysis.md")
    if not sample_file.exists():
        sample_file.write_text(sample_analysis)
        print("✅ 创建示例分析配置")

def validate_installation():
    """验证安装"""
    print("\n🔍 验证安装...")
    
    try:
        # 检查核心模块导入
        sys.path.insert(0, str(Path.cwd()))
        
        # 验证配置加载
        if Path(".env").exists():
            print("✅ 配置文件存在")
        else:
            print("⚠️  配置文件不存在，请复制config.example.env为.env")
        
        # 验证目录结构
        required_dirs = ["src", "data", "tests", "logs"]
        for directory in required_dirs:
            if Path(directory).exists():
                print(f"✅ 目录验证通过: {directory}")
            else:
                print(f"❌ 目录缺失: {directory}")
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")

def main():
    """主函数"""
    print("🚀 智能日志分析器初始化")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 检查系统依赖
    print("\n🔍 检查系统依赖...")
    deps = check_dependencies()
    
    missing_deps = [name for name, available, _ in deps if not available]
    if missing_deps:
        print(f"\n⚠️  缺少依赖: {', '.join(missing_deps)}")
        print("请安装必要的依赖后重新运行此脚本")
    
    # 创建目录结构
    create_directories()
    
    # 创建初始化文件
    create_init_files()
    
    # 创建配置文件
    print("\n⚙️  设置配置文件...")
    create_config_file()
    
    # 创建示例文件
    print("\n📝 创建示例文件...")
    create_sample_files()
    
    # 安装开发工具
    print("\n🛠️  设置开发环境...")
    install_pre_commit()
    
    # 验证安装
    validate_installation()
    
    print("\n🎉 初始化完成!")
    print("\n📋 下一步:")
    print("1. 编辑 .env 文件，配置DeepSeek和RAGFlow参数")
    print("2. 安装Python依赖: pip install -r requirements.txt")
    print("3. 运行测试: pytest tests/")
    print("4. 启动服务: python -m src.cli --help")
    
    if missing_deps:
        print(f"\n⚠️  请先安装缺少的系统依赖: {', '.join(missing_deps)}")

if __name__ == "__main__":
    main() 