#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高性能异步文件管理器
基于Python性能优化最佳实践，提供：
- 异步文件搜索和验证
- 智能路径映射和缓存
- 并发文件内容预览
- 性能监控和基准测试
"""

import asyncio
import aiofiles
import time
from pathlib import Path
from typing import List, Dict, Set, Optional, Tuple, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
from functools import lru_cache
import logging
import re
import hashlib
from collections import defaultdict

logger = logging.getLogger(__name__)

@dataclass
class FileInfo:
    """文件信息结构"""
    path: str
    size: int
    accessible: bool
    preview: str
    last_modified: float
    content_hash: Optional[str] = None

@dataclass
class SearchResult:
    """搜索结果结构"""
    total_files: int
    accessible_files: List[FileInfo]
    search_time: float
    cache_hits: int

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics = defaultdict(list)
        self.start_times = {}
    
    def start_timer(self, operation: str):
        """开始计时"""
        self.start_times[operation] = time.time()
    
    def end_timer(self, operation: str) -> float:
        """结束计时并记录"""
        if operation in self.start_times:
            duration = time.time() - self.start_times[operation]
            self.metrics[operation].append(duration)
            del self.start_times[operation]
            return duration
        return 0.0
    
    def get_stats(self) -> Dict[str, Dict[str, float]]:
        """获取性能统计"""
        stats = {}
        for operation, times in self.metrics.items():
            if times:
                stats[operation] = {
                    'total': sum(times),
                    'average': sum(times) / len(times),
                    'min': min(times),
                    'max': max(times),
                    'count': len(times)
                }
        return stats

class AdvancedFileManager:
    """高性能异步文件管理器"""
    
    def __init__(self, cache_size: int = 1000):
        self.cache_size = cache_size
        self.file_cache: Dict[str, FileInfo] = {}
        self.path_cache: Dict[str, List[str]] = {}
        self.content_cache: Dict[str, str] = {}
        self.monitor = PerformanceMonitor()
        self.executor = ThreadPoolExecutor(max_workers=8)
        
        logger.info(f"高性能文件管理器初始化，缓存大小: {cache_size}")
    
    async def discover_log_files_async(self, 
                                     base_paths: List[str], 
                                     patterns: List[str] = None,
                                     max_preview_size: int = 500) -> SearchResult:
        """异步发现和验证日志文件"""
        self.monitor.start_timer("total_discovery")
        
        if patterns is None:
            patterns = [
                "*.log", 
                "*oauth2*", 
                "*provider*", 
                "*application*", 
                "*error*", 
                "*access*", 
                "*auth*", 
                "*token*", 
                "*redis*",
                "pub-oauth2-provider-*"  # 匹配实际的OAuth2日志文件
            ]
        
        logger.info(f"开始异步文件发现: {len(base_paths)} 个基础路径")
        
        # 异步并发搜索所有路径
        search_tasks = []
        for base_path in base_paths:
            for pattern in patterns:
                task = self._search_path_async(base_path, pattern)
                search_tasks.append(task)
        
        # 并发执行所有搜索任务
        self.monitor.start_timer("file_search")
        search_results = await asyncio.gather(*search_tasks, return_exceptions=True)
        search_time = self.monitor.end_timer("file_search")
        
        # 合并结果并去重
        all_files = set()
        cache_hits = 0
        
        for result in search_results:
            if isinstance(result, Exception):
                logger.warning(f"搜索任务异常: {result}")
                continue
            if isinstance(result, tuple):
                files, hits = result
                all_files.update(files)
                cache_hits += hits
        
        logger.info(f"文件搜索完成: {len(all_files)} 个候选文件")
        
        # 异步验证文件可访问性和获取预览
        verification_tasks = []
        for file_path in all_files:
            task = self._verify_and_preview_async(file_path, max_preview_size)
            verification_tasks.append(task)
        
        self.monitor.start_timer("file_verification")
        verification_results = await asyncio.gather(*verification_tasks, return_exceptions=True)
        verification_time = self.monitor.end_timer("file_verification")
        
        # 处理验证结果
        accessible_files = []
        for result in verification_results:
            if isinstance(result, Exception):
                logger.warning(f"文件验证异常: {result}")
                continue
            if isinstance(result, FileInfo) and result.accessible:
                accessible_files.append(result)
        
        total_time = self.monitor.end_timer("total_discovery")
        
        logger.info(f"文件发现完成:")
        logger.info(f"  总候选文件: {len(all_files)}")
        logger.info(f"  可访问文件: {len(accessible_files)}")
        logger.info(f"  搜索耗时: {search_time:.2f}s")
        logger.info(f"  验证耗时: {verification_time:.2f}s")
        logger.info(f"  总耗时: {total_time:.2f}s")
        logger.info(f"  缓存命中: {cache_hits}")
        
        return SearchResult(
            total_files=len(all_files),
            accessible_files=accessible_files,
            search_time=total_time,
            cache_hits=cache_hits
        )
    
    async def _search_path_async(self, base_path: str, pattern: str) -> Tuple[Set[str], int]:
        """异步搜索单个路径模式"""
        cache_key = f"{base_path}:{pattern}"
        
        # 检查路径缓存
        if cache_key in self.path_cache:
            return set(self.path_cache[cache_key]), 1
        
        try:
            # 使用线程池执行文件系统操作
            loop = asyncio.get_event_loop()
            files = await loop.run_in_executor(
                self.executor, 
                self._sync_search_path, 
                base_path, 
                pattern
            )
            
            # 缓存结果
            self.path_cache[cache_key] = list(files)
            return files, 0
            
        except Exception as e:
            logger.warning(f"路径搜索失败 {base_path}/{pattern}: {e}")
            return set(), 0
    
    def _sync_search_path(self, base_path: str, pattern: str) -> Set[str]:
        """同步搜索路径（在线程池中执行）"""
        import glob
        import os
        
        files = set()
        
        try:
            # 确保base_path存在
            if not os.path.exists(base_path):
                logger.debug(f"路径不存在: {base_path}")
                return files
            
            # 构建搜索模式
            if pattern.startswith("*") and pattern.endswith("*"):
                # 内容模式匹配 - 在文件名中查找关键词
                keyword = pattern.strip("*")
                search_patterns = [
                    os.path.join(base_path, f"*{keyword}*.log"),
                    os.path.join(base_path, f"*{keyword}*"),
                    os.path.join(base_path, "*.log")  # 备选：所有log文件
                ]
            else:
                # 文件名模式匹配
                search_patterns = [os.path.join(base_path, pattern)]
            
            # 使用glob搜索
            for search_pattern in search_patterns:
                try:
                    found_files = glob.glob(search_pattern, recursive=True)
                    for file_path in found_files:
                        if os.path.isfile(file_path):
                            files.add(os.path.abspath(file_path))
                            # 如果是内容匹配模式，检查文件名
                            if "*" in pattern:
                                keyword = pattern.strip("*")
                                if keyword and keyword.lower() in os.path.basename(file_path).lower():
                                    files.add(os.path.abspath(file_path))
                except Exception as glob_error:
                    logger.debug(f"glob搜索失败 {search_pattern}: {glob_error}")
            
            logger.debug(f"在 {base_path} 中找到 {len(files)} 个文件，模式: {pattern}")
        
        except Exception as e:
            logger.debug(f"同步搜索异常 {base_path}/{pattern}: {e}")
        
        return files
    
    async def _verify_and_preview_async(self, file_path: str, max_size: int) -> Optional[FileInfo]:
        """异步验证文件并获取预览"""
        # 检查文件信息缓存
        cache_key = f"file_info:{file_path}"
        if cache_key in self.file_cache:
            return self.file_cache[cache_key]
        
        try:
            # 检查文件基本信息
            path = Path(file_path)
            if not path.exists() or not path.is_file():
                return FileInfo(file_path, 0, False, "", 0)
            
            stat = path.stat()
            
            # 异步读取文件预览
            preview = await self._read_file_preview_async(file_path, max_size)
            
            # 计算内容哈希用于缓存
            content_hash = hashlib.md5(preview.encode()).hexdigest()
            
            file_info = FileInfo(
                path=file_path,
                size=stat.st_size,
                accessible=True,
                preview=preview,
                last_modified=stat.st_mtime,
                content_hash=content_hash
            )
            
            # 缓存文件信息
            self.file_cache[cache_key] = file_info
            
            return file_info
            
        except Exception as e:
            logger.debug(f"文件验证失败 {file_path}: {e}")
            return FileInfo(file_path, 0, False, "", 0)
    
    async def _read_file_preview_async(self, file_path: str, max_size: int) -> str:
        """异步读取文件预览"""
        try:
            async with aiofiles.open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = await f.read(max_size)
                return content.strip()
        except Exception as e:
            logger.debug(f"文件预览读取失败 {file_path}: {e}")
            return ""
    
    async def search_content_async(self, 
                                 files: List[FileInfo], 
                                 keywords: List[str],
                                 context_lines: int = 5) -> Dict[str, List[Dict[str, Any]]]:
        """异步搜索文件内容"""
        self.monitor.start_timer("content_search")
        
        # 并发搜索所有文件
        search_tasks = []
        for file_info in files:
            task = self._search_file_content_async(file_info.path, keywords, context_lines)
            search_tasks.append(task)
        
        results = await asyncio.gather(*search_tasks, return_exceptions=True)
        
        # 合并搜索结果
        combined_results = {}
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.warning(f"内容搜索异常 {files[i].path}: {result}")
                continue
            if result:
                combined_results[files[i].path] = result
        
        search_time = self.monitor.end_timer("content_search")
        logger.info(f"内容搜索完成: {len(combined_results)} 个文件有匹配, 耗时: {search_time:.2f}s")
        
        return combined_results
    
    async def _search_file_content_async(self, 
                                       file_path: str, 
                                       keywords: List[str],
                                       context_lines: int) -> List[Dict[str, Any]]:
        """异步搜索单个文件内容"""
        try:
            # 检查内容缓存
            cache_key = f"content:{file_path}:{':'.join(keywords)}"
            if cache_key in self.content_cache:
                return eval(self.content_cache[cache_key])  # 简单的缓存，生产环境应使用pickle
            
            async with aiofiles.open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = await f.readlines()
            
            matches = []
            for i, line in enumerate(lines):
                for keyword in keywords:
                    if keyword.lower() in line.lower():
                        # 提取上下文
                        start = max(0, i - context_lines)
                        end = min(len(lines), i + context_lines + 1)
                        context = ''.join(lines[start:end])
                        
                        matches.append({
                            'line_number': i + 1,
                            'matched_line': line.strip(),
                            'keyword': keyword,
                            'context': context.strip()
                        })
                        break  # 每行只匹配一次
            
            # 缓存结果
            self.content_cache[cache_key] = str(matches)
            
            return matches
            
        except Exception as e:
            logger.debug(f"文件内容搜索失败 {file_path}: {e}")
            return []
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        return {
            'performance_metrics': self.monitor.get_stats(),
            'cache_stats': {
                'file_cache_size': len(self.file_cache),
                'path_cache_size': len(self.path_cache),
                'content_cache_size': len(self.content_cache)
            }
        }
    
    async def cleanup(self):
        """清理资源"""
        self.executor.shutdown(wait=True)
        logger.info("文件管理器资源清理完成")

# 单例实例
advanced_file_manager = AdvancedFileManager() 