"""
日志搜索引擎 - 大文件优化版

基于ripgrep实现高性能日志搜索，支持大文件流式处理、智能分片和内存控制
"""

import os
import json
import asyncio
import subprocess
import gzip
import shutil
import hashlib
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple, AsyncGenerator
import logging

from core.models import SearchQuery, SearchResult, Evidence
from core.exceptions import LogSearchError, FileProcessingError

logger = logging.getLogger(__name__)

class FileIndex:
    """文件索引管理"""
    
    def __init__(self):
        self.files = {}  # file_path -> file_info
        self.index_time = None
    
    def add_file(self, file_path: str, file_info: Dict[str, Any]):
        """添加文件到索引"""
        self.files[file_path] = file_info
    
    def get_files_by_pattern(self, pattern: str) -> List[str]:
        """根据模式获取文件列表"""
        matching_files = []
        for file_path, file_info in self.files.items():
            if pattern.lower() in file_path.lower() or pattern.lower() in file_info.get('name', '').lower():
                matching_files.append(file_path)
        return matching_files
    
    def get_error_logs(self) -> List[str]:
        """获取错误日志文件"""
        error_files = []
        for file_path, file_info in self.files.items():
            if 'error' in file_path.lower() or file_info.get('type') == 'error_log':
                error_files.append(file_path)
        return error_files

class LogSearchEngine:
    """高性能日志搜索引擎 - 支持大文件处理"""
    
    def __init__(self, config):
        """
        初始化搜索引擎
        
        Args:
            config: 搜索配置对象（可以是SimpleSearchConfig或SearchConfig）
        """
        # 适配不同的配置对象类型
        self.ripgrep_path = getattr(config, 'ripgrep_path', 'rg')
        self.max_results = getattr(config, 'max_results', 100)
        self.max_context_lines = getattr(config, 'max_context_lines', 10)
        
        # 大文件处理配置
        self.large_file_threshold_mb = getattr(config, 'large_file_threshold_mb', 50)  # 50MB以上使用流式处理
        self.max_memory_usage_mb = getattr(config, 'max_memory_usage_mb', 512)  # 最大内存使用
        self.chunk_size_mb = getattr(config, 'chunk_size_mb', 10)  # 分块大小
        self.stream_batch_size = getattr(config, 'stream_batch_size', 1000)  # 流式处理批次大小
        
        self.parallel_searches = getattr(config, 'parallel_searches', 4)
        self.cache_enabled = getattr(config, 'cache_enabled', True)
        self.cache_ttl_minutes = getattr(config, 'cache_ttl_minutes', 60)
        
        # 搜索缓存
        self.search_cache: Dict[str, Tuple[List[SearchResult], datetime]] = {}
        
        # 文件索引
        self.file_index = FileIndex()
        
        logger.info(f"日志搜索引擎初始化完成")
        logger.info(f"  - 大文件阈值: {self.large_file_threshold_mb}MB")
        logger.info(f"  - 最大内存使用: {self.max_memory_usage_mb}MB")
        logger.info(f"  - 分块大小: {self.chunk_size_mb}MB")
        logger.info(f"  - 并发搜索数: {self.parallel_searches}")

    def _is_log_file(self, file_path: Path) -> bool:
        """判断文件是否为日志文件"""
        # 日志文件扩展名
        log_extensions = ['.log', '.txt', '.out', '.err']
        
        # 日志文件名关键词
        log_keywords = ['log', 'error', 'access', 'debug', 'trace', 'oauth', 'auth']
        
        return (file_path.suffix.lower() in log_extensions or
                any(keyword in file_path.name.lower() for keyword in log_keywords))
    
    async def decompress_file(self, file_path: str) -> str:
        """解压缩.gz文件到logs目录"""
        if not file_path.endswith('.gz'):
            return file_path
        
        try:
            file_path_obj = Path(file_path)
            
            # 创建logs目录 - 在压缩包同级目录下
            logs_dir = file_path_obj.parent / "logs"
            logs_dir.mkdir(exist_ok=True)
            
            # 生成输出路径 - 去掉.gz后缀，放在logs目录下
            output_filename = file_path_obj.stem  # 去掉.gz后的文件名
            output_path = str(logs_dir / output_filename)
            
            # 如果已经解压过，直接返回
            if os.path.exists(output_path):
                output_stat = os.path.getmtime(output_path)
                input_stat = os.path.getmtime(file_path)
                if output_stat >= input_stat:
                    logger.info(f"文件已存在且是最新的: logs/{output_filename}")
                    return output_path
            
            logger.info(f"解压缩文件: {file_path_obj.name} -> logs/{output_filename}")
            
            with gzip.open(file_path, 'rb') as f_in:
                with open(output_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            logger.info(f"解压完成: logs/{output_filename}")
            return output_path
            
        except Exception as e:
            logger.error(f"文件解压缩失败: {e}")
            raise FileProcessingError(f"文件解压缩失败: {e}")

    # 1. 实际的.gz文件解压
    async def batch_decompress_files(self, file_paths: List[str]) -> List[str]:
        """批量解压.gz文件"""
        logger.info(f"开始批量解压 {len(file_paths)} 个文件")
        
        decompressed_files = []
        failed_files = []
        
        for i, file_path in enumerate(file_paths, 1):
            try:
                logger.info(f"解压进度: {i}/{len(file_paths)} - {Path(file_path).name}")
                
                if file_path.endswith('.gz'):
                    # 解压缩处理
                    decompressed_path = await self.decompress_file(file_path)
                    decompressed_files.append(decompressed_path)
                    logger.info(f"  解压完成: {Path(decompressed_path).name}")
                else:
                    # 非压缩文件直接添加
                    decompressed_files.append(file_path)
                    logger.info(f"  跳过解压: {Path(file_path).name}")
                    
            except Exception as e:
                logger.error(f"解压失败: {Path(file_path).name} - {e}")
                failed_files.append(file_path)
        
        logger.info(f"批量解压完成: 成功 {len(decompressed_files)} 个, 失败 {len(failed_files)} 个")
        return decompressed_files

    # 2. 大文件分片处理
    async def split_large_file(self, file_path: str) -> List[str]:
        """将大文件分片处理"""
        try:
            file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
            
            if file_size_mb <= self.large_file_threshold_mb:
                logger.info(f"文件大小 {file_size_mb:.1f}MB，无需分片")
                return [file_path]
            
            logger.info(f"文件大小 {file_size_mb:.1f}MB，开始分片处理")
            
            chunk_files = []
            chunk_size_bytes = self.chunk_size_mb * 1024 * 1024
            
            base_name = Path(file_path).stem
            base_dir = Path(file_path).parent
            
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                chunk_num = 0
                current_chunk_data = []
                current_chunk_size = 0
                
                for line in f:
                    line_size = len(line.encode('utf-8'))
                    
                    # 如果当前块已达到大小限制
                    if current_chunk_size + line_size > chunk_size_bytes and current_chunk_data:
                        # 保存当前块
                        chunk_file = base_dir / f"{base_name}_chunk_{chunk_num:03d}.log"
                        with open(chunk_file, 'w', encoding='utf-8') as chunk_f:
                            chunk_f.writelines(current_chunk_data)
                        
                        chunk_files.append(str(chunk_file))
                        logger.info(f"  创建分片 {chunk_num}: {chunk_file.name} ({current_chunk_size/1024/1024:.1f}MB)")
                        
                        # 重置块数据
                        current_chunk_data = []
                        current_chunk_size = 0
                        chunk_num += 1
                    
                    # 添加行到当前块
                    current_chunk_data.append(line)
                    current_chunk_size += line_size
                
                # 处理最后一个块
                if current_chunk_data:
                    chunk_file = base_dir / f"{base_name}_chunk_{chunk_num:03d}.log"
                    with open(chunk_file, 'w', encoding='utf-8') as chunk_f:
                        chunk_f.writelines(current_chunk_data)
                    
                    chunk_files.append(str(chunk_file))
                    logger.info(f"  创建分片 {chunk_num}: {chunk_file.name} ({current_chunk_size/1024/1024:.1f}MB)")
            
            logger.info(f"大文件分片完成: 生成 {len(chunk_files)} 个分片文件")
            return chunk_files
            
        except Exception as e:
            logger.error(f"大文件分片失败: {e}")
            raise FileProcessingError(f"大文件分片失败: {e}")

    # 3. 智能文件扫描
    async def smart_scan_directory(self, directory: str, patterns: List[str] = None) -> Dict[str, Any]:
        """智能扫描目录中的日志文件"""
        directory_path = Path(directory)
        if not directory_path.exists():
            raise FileProcessingError(f"目录不存在: {directory}")
        
        logger.info(f"开始智能扫描目录: {directory}")
        
        scan_result = {
            'directory': directory,
            'scan_time': datetime.now().isoformat(),
            'total_files': 0,
            'log_files': [],
            'error_logs': [],
            'compressed_files': [],
            'large_files': [],
            'filtered_files': [],
            'file_types': {},
            'total_size_mb': 0
        }
        
        # 默认搜索模式
        if not patterns:
            patterns = ['*.log', '*.gz', '*error*', '*oauth*', '*auth*']
        
        all_files = []
        
        # 收集所有文件
        for pattern in patterns:
            files = list(directory_path.glob(pattern))
            all_files.extend(files)
        
        # 去重
        all_files = list(set(all_files))
        scan_result['total_files'] = len(all_files)
        
        logger.info(f"发现 {len(all_files)} 个文件，开始智能分类")
        
        for file_path in all_files:
            try:
                if not file_path.is_file():
                    continue
                
                file_info = await self._analyze_file(file_path)
                scan_result['total_size_mb'] += file_info['size_mb']
                
                # 文件分类
                if file_info['is_log_file']:
                    scan_result['log_files'].append(file_info)
                    
                    # 错误日志特殊标记
                    if 'error' in file_path.name.lower():
                        scan_result['error_logs'].append(file_info)
                        file_info['priority'] = 'high'  # 高优先级
                
                if file_info['is_compressed']:
                    scan_result['compressed_files'].append(file_info)
                
                if file_info['is_large_file']:
                    scan_result['large_files'].append(file_info)
                
                # 文件类型统计
                file_type = file_info['file_type']
                scan_result['file_types'][file_type] = scan_result['file_types'].get(file_type, 0) + 1
                
                # 添加到索引
                self.file_index.add_file(str(file_path), file_info)
                
            except Exception as e:
                logger.warning(f"分析文件失败 {file_path}: {e}")
        
        # 智能过滤 - 优先级排序
        scan_result['filtered_files'] = await self._smart_filter_files(scan_result)
        
        logger.info(f"智能扫描完成:")
        logger.info(f"  - 总文件数: {scan_result['total_files']}")
        logger.info(f"  - 日志文件: {len(scan_result['log_files'])} 个")
        logger.info(f"  - 错误日志: {len(scan_result['error_logs'])} 个")
        logger.info(f"  - 压缩文件: {len(scan_result['compressed_files'])} 个")
        logger.info(f"  - 大文件: {len(scan_result['large_files'])} 个")
        logger.info(f"  - 总大小: {scan_result['total_size_mb']:.1f}MB")
        
        return scan_result

    async def _analyze_file(self, file_path: Path) -> Dict[str, Any]:
        """分析单个文件的属性"""
        try:
            stat = file_path.stat()
            size_mb = stat.st_size / (1024 * 1024)
            
            file_info = {
                'path': str(file_path),
                'name': file_path.name,
                'size_mb': size_mb,
                'size_bytes': stat.st_size,
                'modified_time': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'file_type': file_path.suffix.lower(),
                'is_log_file': self._is_log_file(file_path),
                'is_compressed': file_path.suffix.lower() in ['.gz', '.zip', '.tar', '.bz2'],
                'is_large_file': size_mb > self.large_file_threshold_mb,
                'priority': 'normal'
            }
            
            # 智能类型检测
            if 'error' in file_path.name.lower():
                file_info['log_category'] = 'error'
                file_info['priority'] = 'high'
            elif 'oauth' in file_path.name.lower() or 'auth' in file_path.name.lower():
                file_info['log_category'] = 'auth'
                file_info['priority'] = 'high'
            elif 'access' in file_path.name.lower():
                file_info['log_category'] = 'access'
                file_info['priority'] = 'medium'
            else:
                file_info['log_category'] = 'general'
            
            return file_info
            
        except Exception as e:
            logger.error(f"文件分析失败 {file_path}: {e}")
            return {
                'path': str(file_path),
                'name': file_path.name,
                'error': str(e),
                'is_log_file': False,
                'is_compressed': False,
                'is_large_file': False,
                'priority': 'low'
            }

    async def _smart_filter_files(self, scan_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """智能过滤和排序文件"""
        all_files = scan_result['log_files'][:]
        
        # 优先级排序
        priority_order = {'high': 0, 'medium': 1, 'normal': 2, 'low': 3}
        
        def file_priority_score(file_info):
            priority_score = priority_order.get(file_info.get('priority', 'normal'), 2)
            size_score = min(file_info.get('size_mb', 0) / 100, 1)  # 大小权重
            
            # 错误日志和认证日志优先
            category_bonus = 0
            if file_info.get('log_category') in ['error', 'auth']:
                category_bonus = -0.5
            
            return priority_score + size_score + category_bonus
        
        # 排序
        all_files.sort(key=file_priority_score)
        
        # 限制数量（避免处理太多文件）
        max_files = 20
        filtered_files = all_files[:max_files]
        
        logger.info(f"智能过滤: {len(all_files)} -> {len(filtered_files)} 个文件")
        
        return filtered_files

    # 4. 文件索引建立
    async def build_file_index(self, scan_result: Dict[str, Any]) -> Dict[str, Any]:
        """基于扫描结果建立文件索引"""
        logger.info("开始建立文件索引")
        
        index_info = {
            'index_time': datetime.now().isoformat(),
            'total_files': len(scan_result['log_files']),
            'indexed_files': 0,
            'index_structure': {
                'by_category': {},
                'by_priority': {},
                'by_size': {},
                'by_type': {}
            },
            'quick_access': {
                'error_logs': [],
                'auth_logs': [],
                'large_files': [],
                'recent_files': []
            }
        }
        
        for file_info in scan_result['log_files']:
            try:
                # 按类别索引
                category = file_info.get('log_category', 'general')
                if category not in index_info['index_structure']['by_category']:
                    index_info['index_structure']['by_category'][category] = []
                index_info['index_structure']['by_category'][category].append(file_info['path'])
                
                # 按优先级索引
                priority = file_info.get('priority', 'normal')
                if priority not in index_info['index_structure']['by_priority']:
                    index_info['index_structure']['by_priority'][priority] = []
                index_info['index_structure']['by_priority'][priority].append(file_info['path'])
                
                # 按大小索引
                size_category = 'large' if file_info.get('is_large_file', False) else 'normal'
                if size_category not in index_info['index_structure']['by_size']:
                    index_info['index_structure']['by_size'][size_category] = []
                index_info['index_structure']['by_size'][size_category].append(file_info['path'])
                
                # 快速访问索引
                if category == 'error':
                    index_info['quick_access']['error_logs'].append(file_info['path'])
                elif category == 'auth':
                    index_info['quick_access']['auth_logs'].append(file_info['path'])
                
                if file_info.get('is_large_file', False):
                    index_info['quick_access']['large_files'].append(file_info['path'])
                
                index_info['indexed_files'] += 1
                
            except Exception as e:
                logger.warning(f"索引文件失败 {file_info.get('path', 'unknown')}: {e}")
        
        # 更新全局文件索引时间
        self.file_index.index_time = datetime.now()
        
        logger.info(f"文件索引建立完成:")
        logger.info(f"  - 索引文件数: {index_info['indexed_files']}")
        logger.info(f"  - 错误日志: {len(index_info['quick_access']['error_logs'])} 个")
        logger.info(f"  - 认证日志: {len(index_info['quick_access']['auth_logs'])} 个")
        logger.info(f"  - 大文件: {len(index_info['quick_access']['large_files'])} 个")
        
        return index_info

    # 综合文件准备方法
    async def prepare_files_for_analysis(self, directory: str, target_patterns: List[str] = None) -> Dict[str, Any]:
        """完整的文件准备流程：扫描 → 解压 → 分片 → 索引"""
        logger.info(f"开始完整文件准备流程: {directory}")
        
        preparation_result = {
            'status': 'success',
            'directory': directory,
            'start_time': datetime.now().isoformat(),
            'scan_result': None,
            'decompressed_files': [],
            'chunked_files': [],
            'index_info': None,
            'ready_files': [],
            'processing_summary': {}
        }
        
        try:
            # Step 1: 智能文件扫描
            logger.info("Step 1: 执行智能文件扫描")
            scan_result = await self.smart_scan_directory(directory, target_patterns)
            preparation_result['scan_result'] = scan_result
            
            # Step 2: 批量解压文件
            logger.info("Step 2: 批量解压压缩文件")
            compressed_files = [f['path'] for f in scan_result['compressed_files']]
            if compressed_files:
                decompressed_files = await self.batch_decompress_files(compressed_files)
                preparation_result['decompressed_files'] = decompressed_files
                logger.info(f"解压完成: {len(decompressed_files)} 个文件")
            else:
                logger.info("无压缩文件需要解压")
            
            # Step 3: 大文件分片处理
            logger.info("Step 3: 处理大文件分片")
            large_files = [f['path'] for f in scan_result['large_files']]
            all_chunked_files = []
            
            for large_file in large_files:
                chunked_files = await self.split_large_file(large_file)
                all_chunked_files.extend(chunked_files)
            
            preparation_result['chunked_files'] = all_chunked_files
            if all_chunked_files:
                logger.info(f"大文件分片完成: 生成 {len(all_chunked_files)} 个分片")
            else:
                logger.info("无大文件需要分片")
            
            # Step 4: 建立文件索引
            logger.info("Step 4: 建立文件索引")
            index_info = await self.build_file_index(scan_result)
            preparation_result['index_info'] = index_info
            
            # Step 5: 准备最终的分析文件列表
            ready_files = []
            
            # 优先添加错误日志
            ready_files.extend(index_info['quick_access']['error_logs'])
            
            # 添加认证日志
            ready_files.extend(index_info['quick_access']['auth_logs'])
            
            # 添加其他重要文件
            for file_info in scan_result['filtered_files']:
                if file_info['path'] not in ready_files:
                    ready_files.append(file_info['path'])
            
            preparation_result['ready_files'] = ready_files
            preparation_result['processing_summary'] = {
                'total_files_found': scan_result['total_files'],
                'log_files_found': len(scan_result['log_files']),
                'files_decompressed': len(preparation_result['decompressed_files']),
                'files_chunked': len(preparation_result['chunked_files']),
                'files_indexed': index_info['indexed_files'],
                'files_ready_for_analysis': len(ready_files)
            }
            
            preparation_result['end_time'] = datetime.now().isoformat()
            
            logger.info("完整文件准备流程完成:")
            logger.info(f"  - 发现文件: {scan_result['total_files']} 个")
            logger.info(f"  - 日志文件: {len(scan_result['log_files'])} 个")
            logger.info(f"  - 解压文件: {len(preparation_result['decompressed_files'])} 个")
            logger.info(f"  - 分片文件: {len(preparation_result['chunked_files'])} 个")
            logger.info(f"  - 准备分析: {len(ready_files)} 个文件")
            
            return preparation_result
            
        except Exception as e:
            logger.error(f"文件准备流程失败: {e}")
            preparation_result['status'] = 'error'
            preparation_result['error'] = str(e)
            preparation_result['end_time'] = datetime.now().isoformat()
            raise FileProcessingError(f"文件准备流程失败: {e}")
    
    def _verify_ripgrep(self):
        """验证ripgrep工具是否可用"""
        try:
            result = subprocess.run([self.ripgrep_path, '--version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                logger.info(f"✅ ripgrep可用: {result.stdout.split()[1]}")
                return True
            else:
                logger.warning("⚠️  ripgrep可能不可用，搜索功能可能受限")
                return False
        except (subprocess.TimeoutExpired, FileNotFoundError) as e:
            logger.warning(f"⚠️  ripgrep验证失败: {e}")
            return False
    
    async def search_in_file(self, query: SearchQuery, file_path: str) -> List[SearchResult]:
        """在指定文件中搜索关键词 - 实际搜索实现"""
        results = []
        
        try:
            logger.info(f"开始搜索文件: {file_path}")
            logger.info(f"使用关键词: {query.keywords}")
            
            file_path_obj = Path(file_path)
            if not file_path_obj.exists():
                logger.warning(f"文件不存在: {file_path}")
                return []
            
            file_size = file_path_obj.stat().st_size
            logger.info(f"文件大小: {file_size / 1024 / 1024:.2f}MB")
            
            # 选择搜索策略
            if file_size > self.large_file_threshold_mb * 1024 * 1024:
                logger.info(f"使用大文件流式搜索策略")
                return await self._search_large_file_streaming(query, file_path)
            else:
                logger.info(f"使用标准文件搜索策略")
                return await self._search_standard_file(query, file_path)
                
        except Exception as e:
            logger.error(f"文件搜索失败 {file_path}: {e}")
            return []
    
    async def _search_large_file_streaming(self, query: SearchQuery, file_path: str) -> List[SearchResult]:
        """大文件流式搜索 - 基于ripgrep"""
        all_results = []
        
        try:
            # 验证ripgrep可用性
            if not await self._verify_ripgrep():
                logger.warning("ripgrep不可用，使用Python搜索")
                return await self._search_standard_file(query, file_path)
            
            # 对每个关键词执行ripgrep搜索
            for keyword in query.keywords:
                if not keyword.strip():
                    continue
                    
                logger.info(f"搜索关键词: {keyword}")
                keyword_results = await self._ripgrep_streaming_search(keyword, file_path, query)
                
                # 合并结果
                all_results.extend(keyword_results)
                logger.info(f"关键词 '{keyword}' 找到 {len(keyword_results)} 条结果")
            
            # 智能合并和去重
            merged_results = self._smart_merge_results(all_results)
            logger.info(f"合并后共 {len(merged_results)} 条唯一结果")
            
            return merged_results
            
        except Exception as e:
            logger.error(f"大文件流式搜索失败: {e}")
            return []
    
    async def _ripgrep_streaming_search(self, keyword: str, file_path: str, query: SearchQuery) -> List[SearchResult]:
        """使用ripgrep进行流式搜索"""
        results = []
        
        try:
            # 构建ripgrep命令
            cmd = [
                'rg',
                '--json',  # JSON输出
                '--context', '3',  # 上下文行数
                '--byte-offset',   # 字节偏移
                '--line-number',   # 行号
                '--ignore-case',   # 忽略大小写
                '--no-heading',    # 不显示文件头
                '--color', 'never', # 不使用颜色
                keyword,
                file_path
            ]
            
            logger.info(f"执行ripgrep命令: {' '.join(cmd)}")
            
            # 异步执行ripgrep
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            # 流式处理输出
            async for batch_results in self._process_ripgrep_stream(process.stdout, file_path, keyword):
                results.extend(batch_results)
                
                # 内存控制
                if len(results) > 1000:  # 限制结果数量
                    logger.warning(f"结果数量达到上限，停止搜索")
                    break
            
            # 等待进程完成
            await process.wait()
            
            return results
            
        except Exception as e:
            logger.error(f"ripgrep搜索失败: {e}")
            return []
    
    async def _process_ripgrep_stream(self, stdout: asyncio.StreamReader, file_path: str, keyword: str) -> AsyncGenerator[List[SearchResult], None]:
        """流式处理ripgrep输出"""
        batch_results = []
        
        try:
            while True:
                line = await stdout.readline()
                if not line:
                    break
                
                try:
                    # 解析JSON行
                    json_data = json.loads(line.decode('utf-8').strip())
                    
                    # 处理匹配结果
                    if json_data.get('type') == 'match':
                        result = self._parse_single_ripgrep_line(json_data, file_path, keyword)
                        if result:
                            batch_results.append(result)
                    
                    # 批量返回结果
                    if len(batch_results) >= self.stream_batch_size:
                        yield batch_results.copy()
                        batch_results.clear()
                        
                except json.JSONDecodeError:
                    # 跳过无效JSON行
                    continue
                except Exception as e:
                    logger.warning(f"处理ripgrep行失败: {e}")
                    continue
            
            # 返回剩余结果
            if batch_results:
                yield batch_results
                
        except Exception as e:
            logger.error(f"处理ripgrep流失败: {e}")
    
    def _parse_single_ripgrep_line(self, json_data: dict, file_path: str, keyword: str) -> Optional[SearchResult]:
        """解析单个ripgrep JSON结果"""
        try:
            data = json_data.get('data', {})
            
            # 提取基本信息
            line_number = data.get('line_number', 0)
            line_text = data.get('lines', {}).get('text', '')
            byte_offset = data.get('absolute_offset', 0)
            
            # 提取时间戳
            timestamp = self._extract_timestamp(line_text)
            
            # 计算置信度
            confidence = self._calculate_confidence_score(line_text, keyword)
            
            # 构建SearchResult
            return SearchResult(
                file_path=file_path,
                line_number=line_number,
                matched_content=line_text.strip(),
                keyword=keyword,
                timestamp=timestamp,
                confidence_score=confidence,
                byte_offset=byte_offset,
                context_before=[],  # ripgrep context会在后续处理
                context_after=[]
            )
            
        except Exception as e:
            logger.warning(f"解析ripgrep结果失败: {e}")
            return None
    
    async def _search_standard_file(self, query: SearchQuery, file_path: str) -> List[SearchResult]:
        """标准文件搜索 - Python实现"""
        results = []
        
        try:
            logger.info(f"使用Python搜索文件: {file_path}")
            
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                line_text = line.strip()
                
                # 检查每个关键词
                for keyword in query.keywords:
                    if not keyword.strip():
                        continue
                        
                    if keyword.lower() in line_text.lower():
                        # 提取时间戳
                        timestamp = self._extract_timestamp(line_text)
                        
                        # 计算置信度
                        confidence = self._calculate_confidence_score(line_text, keyword)
                        
                        # 获取上下文
                        context_before = [lines[i].strip() for i in range(max(0, line_num-3), line_num-1)]
                        context_after = [lines[i].strip() for i in range(line_num, min(len(lines), line_num+3))]
                        
                        result = SearchResult(
                            file_path=file_path,
                            line_number=line_num,
                            matched_content=line_text,
                            keyword=keyword,
                            timestamp=timestamp,
                            confidence_score=confidence,
                            byte_offset=0,  # Python搜索不提供字节偏移
                            context_before=context_before,
                            context_after=context_after
                        )
                        
                        results.append(result)
                        
                        # 限制结果数量
                        if len(results) >= 500:
                            logger.warning(f"Python搜索结果达到上限，停止搜索")
                            return results
            
            logger.info(f"Python搜索完成，找到 {len(results)} 条结果")
            return results
            
        except Exception as e:
            logger.error(f"标准文件搜索失败: {e}")
            return []
    
    def _smart_merge_results(self, results: List[SearchResult]) -> List[SearchResult]:
        """智能合并搜索结果，去重并排序"""
        if not results:
            return []
        
        # 按文件路径和行号去重
        unique_results = {}
        for result in results:
            key = (result.file_path, result.line_number)
            if key not in unique_results:
                unique_results[key] = result
            else:
                # 保留置信度更高的结果
                if result.confidence_score > unique_results[key].confidence_score:
                    unique_results[key] = result
        
        # 转换回列表并排序
        merged_results = list(unique_results.values())
        
        # 按置信度和时间戳排序
        merged_results.sort(
            key=lambda x: (-x.confidence_score, x.timestamp or datetime.min),
            reverse=False
        )
        
        return merged_results
    
    async def _calculate_confidence_score_dynamic(self, text: str, keyword: str, analysis_framework=None) -> float:
        """动态置信度计算 - 完全基于分析框架，无硬编码"""
        if not text or not keyword:
            return 0.0
        
        try:
            # 如果有分析框架，使用动态引擎评估
            if analysis_framework and hasattr(self, 'dynamic_engine'):
                return await self.dynamic_engine.evaluate_content_relevance(text, analysis_framework)
            
            # 基础计算（仅在无分析框架时使用）
            text_lower = text.lower()
            keyword_lower = keyword.lower()
            
            score = 0.0
            
            # 基础匹配分数
            if keyword_lower in text_lower:
                score += 0.4
            
            # 完整词匹配加分
            import re
            if re.search(rf'\b{re.escape(keyword_lower)}\b', text_lower):
                score += 0.4
            
            # 上下文相关性评分（替代硬编码关键词）
            context_indicators = len(re.findall(r'\b(?:error|exception|failed|warning|critical)\b', text_lower))
            if context_indicators > 0:
                score += min(0.2, context_indicators * 0.1)
            
            return min(score, 1.0)  # 限制在0-1范围
            
        except Exception as e:
            # 降级处理
            self.logger.error(f"动态置信度计算失败: {e}")
            return self._fallback_confidence_score(text, keyword)
    
    def _fallback_confidence_score(self, text: str, keyword: str) -> float:
        """降级置信度计算 - 最基础的匹配算法"""
        if not text or not keyword:
            return 0.0
        
        text_lower = text.lower()
        keyword_lower = keyword.lower()
        
        if keyword_lower in text_lower:
            return 0.6
        
        return 0.0
    
    def _extract_timestamp(self, text: str) -> Optional[datetime]:
        """从日志行中提取时间戳"""
        import re
        from datetime import datetime
        
        # 常见时间戳格式
        timestamp_patterns = [
            r'\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}\.?\d*',  # YYYY-MM-DD HH:MM:SS.sss
            r'\d{4}/\d{2}/\d{2}\s+\d{2}:\d{2}:\d{2}',        # YYYY/MM/DD HH:MM:SS
            r'\d{2}:\d{2}:\d{2}\.?\d*',                       # HH:MM:SS.sss
        ]
        
        for pattern in timestamp_patterns:
            match = re.search(pattern, text)
            if match:
                timestamp_str = match.group()
                try:
                    # 尝试解析时间戳
                    if '-' in timestamp_str and len(timestamp_str) > 10:
                        return datetime.strptime(timestamp_str[:19], '%Y-%m-%d %H:%M:%S')
                    elif '/' in timestamp_str:
                        return datetime.strptime(timestamp_str, '%Y/%m/%d %H:%M:%S')
                except ValueError:
                    continue
        
        return None
    
    async def _verify_ripgrep(self) -> bool:
        """验证ripgrep是否可用"""
        try:
            process = await asyncio.create_subprocess_exec(
                'rg', '--version',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            await process.wait()
            return process.returncode == 0
        except Exception:
            return False
    
    async def search_multiple_files(self, query: SearchQuery, directory: str) -> List[SearchResult]:
        """在多个文件中并行搜索 - 支持大文件优化"""
        try:
            # 扫描日志文件
            log_files = self._scan_log_files(directory)
            
            if not log_files:
                logger.warning(f"目录 {directory} 中未找到日志文件")
                return []
            
            # 并行搜索文件
            search_tasks = []
            semaphore = asyncio.Semaphore(self.parallel_searches)
            
            async def search_single_file(file_path):
                async with semaphore:
                    return await self.search_in_file(query, file_path)
            
            for file_path in log_files:
                task = asyncio.create_task(search_single_file(file_path))
                search_tasks.append(task)
            
            # 收集所有结果
            all_results = []
            results_list = await asyncio.gather(*search_tasks, return_exceptions=True)
            
            for results in results_list:
                if not isinstance(results, Exception):
                    all_results.extend(results)
            
            # 全局智能合并和排序
            all_results = self._smart_merge_results(all_results)
            
            logger.info(f"多文件搜索完成，共找到 {len(all_results)} 个结果")
            return all_results[:self.max_results]
            
        except Exception as e:
            logger.error(f"多文件搜索失败: {e}")
            raise LogSearchError(f"多文件搜索失败: {e}")
    
    async def _search_keyword_in_file(self, keyword: str, file_path: str, query: SearchQuery) -> List[SearchResult]:
        """在文件中搜索单个关键词 - 标准方法"""
        # 检查缓存
        cache_key = f"{keyword}:{file_path}:{query.context_lines or self.max_context_lines}"
        if self.cache_enabled and cache_key in self.search_cache:
            cached_results, cache_time = self.search_cache[cache_key]
            if (datetime.now() - cache_time).total_seconds() < self.cache_ttl_minutes * 60:
                return cached_results
        
        results = []
        
        try:
            # 构建ripgrep命令 - 标准搜索
            cmd = [
                self.ripgrep_path,
                '--json',
                '--context', str(query.context_lines or self.max_context_lines),
                '--line-number',
                '--max-count', str(self.max_results),
                '--ignore-case',
                keyword,
                file_path
            ]
            
            # 执行搜索
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                # 解析JSON输出
                results = self._parse_ripgrep_json(stdout.decode('utf-8'), file_path, keyword)
            elif process.returncode == 1:
                # 未找到匹配，这是正常情况
                pass
            else:
                logger.warning(f"ripgrep搜索警告: {stderr.decode('utf-8')}")
            
            # 缓存结果
            if self.cache_enabled:
                self.search_cache[cache_key] = (results, datetime.now())
            
        except Exception as e:
            logger.error(f"关键词搜索失败 '{keyword}' in {file_path}: {e}")
        
        return results
    
    def _parse_ripgrep_json(self, json_output: str, file_path: str, keyword: str) -> List[SearchResult]:
        """解析ripgrep的JSON输出 - 标准方法"""
        results = []
        
        try:
            lines = json_output.strip().split('\n')
            current_match = None
            context_lines = []
            
            for line in lines:
                if not line.strip():
                    continue
                
                try:
                    json_data = json.loads(line)
                    
                    if json_data.get('type') == 'match':
                        data = json_data.get('data', {})
                        
                        # 提取匹配信息
                        line_number = data.get('line_number', 0)
                        line_text = data.get('lines', {}).get('text', '')
                        byte_offset = data.get('absolute_offset', 0)
                        
                        # 提取时间戳
                        timestamp = self._extract_timestamp(line_text)
                        
                        # 计算置信度
                        confidence = self._calculate_confidence_score(line_text, keyword)
                        
                        # 创建SearchResult
                        result = SearchResult(
                            file_path=file_path,
                            line_number=line_number,
                            matched_content=line_text.strip(),
                            keyword=keyword,
                            timestamp=timestamp,
                            confidence_score=confidence,
                            byte_offset=byte_offset,
                            context_before=[],
                            context_after=[]
                        )
                        
                        results.append(result)
                        
                    elif json_data.get('type') == 'context':
                        # 处理上下文行（如果需要）
                        pass
                        
                except json.JSONDecodeError:
                    # 跳过无效JSON行
                    continue
                except Exception as e:
                    logger.warning(f"解析ripgrep JSON行失败: {e}")
                    continue
            
        except Exception as e:
            logger.error(f"解析ripgrep JSON输出失败: {e}")
        
        return results
    
    def _deduplicate_results(self, results: List[SearchResult]) -> List[SearchResult]:
        """去重搜索结果"""
        if not results:
            return []
        
        unique_results = {}
        for result in results:
            key = f"{result.file_path}:{result.line_number}"
            if key not in unique_results or result.confidence_score > unique_results[key].confidence_score:
                unique_results[key] = result
        
        return list(unique_results.values())
    
    def _extract_timestamp(self, text: str) -> Optional[datetime]:
        """从日志文本中提取时间戳"""
        import re
        
        # 常见的时间戳格式
        patterns = [
            r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}',  # 2024-01-01 12:00:00
            r'\d{2}/\d{2}/\d{4} \d{2}:\d{2}:\d{2}',  # 01/01/2024 12:00:00
            r'\d{2}-\d{2}-\d{4} \d{2}:\d{2}:\d{2}',  # 01-01-2024 12:00:00
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                try:
                    timestamp_str = match.group()
                    # 尝试解析时间戳
                    for fmt in ['%Y-%m-%d %H:%M:%S', '%m/%d/%Y %H:%M:%S', '%m-%d-%Y %H:%M:%S']:
                        try:
                            return datetime.strptime(timestamp_str, fmt)
                        except ValueError:
                            continue
                except Exception:
                    pass
        
        return None
    
    def _scan_log_files(self, directory: str) -> List[str]:
        """扫描目录中的日志文件"""
        log_files = []
        
        try:
            dir_path = Path(directory)
            if not dir_path.exists():
                logger.warning(f"目录不存在: {directory}")
                return []
            
            # 扫描所有日志文件
            for file_path in dir_path.glob('**/*'):
                if file_path.is_file() and self._is_log_file(file_path):
                    log_files.append(str(file_path))
            
            logger.info(f"扫描到 {len(log_files)} 个日志文件")
            
        except Exception as e:
            logger.error(f"扫描日志文件失败: {e}")
        
        return log_files


class LogFileManager:
    """日志文件管理器"""
    
    def __init__(self, data_dir: str):
        """
        初始化文件管理器
        
        Args:
            data_dir: 数据目录路径
        """
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"LogFileManager初始化 - 数据目录: {self.data_dir}")
    
    async def scan_and_prepare(self, log_paths: List[str]) -> List[str]:
        """扫描和准备日志文件"""
        prepared_files = []
        
        for log_path in log_paths:
            try:
                path = Path(log_path)
                
                if path.is_file():
                    # 单个文件
                    if path.suffix == '.gz':
                        decompressed = await self.decompress_file(str(path))
                        prepared_files.append(decompressed)
                    else:
                        prepared_files.append(str(path))
                
                elif path.is_dir():
                    # 目录，扫描其中的文件
                    files = await self.scan_log_files(str(path))
                    for file_path in files:
                        if file_path.endswith('.gz'):
                            decompressed = await self.decompress_file(file_path)
                            prepared_files.append(decompressed)
                        else:
                            prepared_files.append(file_path)
                
                else:
                    logger.warning(f"路径不存在: {log_path}")
            
            except Exception as e:
                logger.error(f"准备文件失败 {log_path}: {e}")
        
        logger.info(f"文件准备完成，共 {len(prepared_files)} 个文件")
        return prepared_files
    
    async def scan_log_files(self, directory: str) -> List[str]:
        """扫描目录中的日志文件"""
        log_files = []
        
        try:
            path = Path(directory)
            
            for file_path in path.rglob('*'):
                if file_path.is_file():
                    # 检查是否是日志文件
                    if self._is_log_file(file_path):
                        log_files.append(str(file_path))
            
        except Exception as e:
            logger.error(f"目录扫描失败: {e}")
            raise FileProcessingError(f"目录扫描失败: {e}")
        
        return log_files
    

    
    async def decompress_file(self, file_path: str) -> str:
        """解压缩.gz文件到logs目录"""
        if not file_path.endswith('.gz'):
            return file_path
        
        try:
            file_path_obj = Path(file_path)
            
            # 创建logs目录 - 在压缩包同级目录下
            logs_dir = file_path_obj.parent / "logs"
            logs_dir.mkdir(exist_ok=True)
            
            # 生成输出路径 - 去掉.gz后缀，放在logs目录下
            output_filename = file_path_obj.stem  # 去掉.gz后的文件名
            output_path = str(logs_dir / output_filename)
            
            # 如果已经解压过，直接返回
            if os.path.exists(output_path):
                output_stat = os.path.getmtime(output_path)
                input_stat = os.path.getmtime(file_path)
                if output_stat >= input_stat:
                    logger.info(f"文件已存在且是最新的: logs/{output_filename}")
                    return output_path
            
            logger.info(f"解压缩文件: {file_path_obj.name} -> logs/{output_filename}")
            
            with gzip.open(file_path, 'rb') as f_in:
                with open(output_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            logger.info(f"解压完成: logs/{output_filename}")
            return output_path
            
        except Exception as e:
            logger.error(f"文件解压缩失败: {e}")
            raise FileProcessingError(f"文件解压缩失败: {e}")
    
    def _is_log_file(self, file_path: Path) -> bool:
        """判断文件是否为日志文件"""
        # 日志文件扩展名
        log_extensions = ['.log', '.txt', '.out', '.err']
        
        # 日志文件名关键词
        log_keywords = ['log', 'error', 'access', 'debug', 'trace', 'oauth', 'auth']
        
        return (file_path.suffix.lower() in log_extensions or
                any(keyword in file_path.name.lower() for keyword in log_keywords))


class SmartLogReader:
    """智能日志阅读器"""
    
    def __init__(self, config=None):
        """
        初始化智能阅读器
        
        Args:
            config: 配置对象（可选）
        """
        self.max_lines_per_read = 1000
        self.chunk_size = 8192
        
        if config:
            self.max_lines_per_read = getattr(config, 'max_lines_per_read', 1000)
            self.chunk_size = getattr(config, 'chunk_size', 8192)
        
        logger.info(f"SmartLogReader初始化 - 最大行数: {self.max_lines_per_read}")
    
    async def read_with_strategy(self, file_path: str, strategy: str = 'auto', max_lines: int = None) -> str:
        """根据策略读取日志内容"""
        max_lines = max_lines or self.max_lines_per_read
        
        try:
            if strategy == 'head':
                return await self._read_head(file_path, max_lines)
            elif strategy == 'tail':
                return await self._read_tail(file_path, max_lines)
            elif strategy == 'search':
                return await self._read_with_search(file_path, max_lines)
            else:  # auto
                return await self._read_auto(file_path, max_lines)
        
        except Exception as e:
            logger.error(f"文件读取失败 {file_path}: {e}")
            raise FileProcessingError(f"文件读取失败: {e}")
    
    async def _read_head(self, file_path: str, max_lines: int) -> str:
        """读取文件开头"""
        lines = []
        
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            for i, line in enumerate(f):
                if i >= max_lines:
                    break
                lines.append(line)
        
        return ''.join(lines)
    
    async def _read_tail(self, file_path: str, max_lines: int) -> str:
        """读取文件末尾"""
        lines = []
        
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            # 使用简单的方式读取末尾
            all_lines = f.readlines()
            lines = all_lines[-max_lines:]
        
        return ''.join(lines)
    
    async def _read_with_search(self, file_path: str, max_lines: int) -> str:
        """基于搜索结果智能读取"""
        # 这里可以结合搜索结果，读取相关的日志片段
        # 目前简化为读取文件的中间部分
        return await self._read_auto(file_path, max_lines)
    
    async def _read_auto(self, file_path: str, max_lines: int) -> str:
        """自动选择最佳读取策略"""
        try:
            file_size = os.path.getsize(file_path)
            
            # 小文件直接全读
            if file_size < 1024 * 1024:  # 1MB
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    lines = content.split('\n')
                    if len(lines) <= max_lines:
                        return content
            
            # 大文件读取头尾
            head_content = await self._read_head(file_path, max_lines // 2)
            tail_content = await self._read_tail(file_path, max_lines // 2)
            
            return f"{head_content}\n... [文件中间部分已省略] ...\n{tail_content}"
        
        except Exception as e:
            logger.error(f"自动读取失败: {e}")
            # 降级到简单的头部读取
            return await self._read_head(file_path, max_lines) 