"""
智能日志分析器 - 核心分析代理

本模块实现了基于Google ADK框架的智能日志分析代理，集成DeepSeek模型、RAGFlow知识库
和高性能日志搜索引擎，提供完全去硬编码的动态分析能力。

核心功能：
- 5阶段智能分析流程：查询方法 → 文件准备 → 智能搜索 → 深度分析 → 报告生成
- 基于RAGFlow知识库的动态分析方法获取
- 高性能大规模日志处理（支持GB级文件）
- 完全通用化设计，支持任意类型的后台系统日志问题

技术特点：
- Google ADK标准Agent架构
- 异步处理和性能监控
- 动态置信度评估和智能决策
- 多轮对话支持和上下文管理

适用场景：
- 新能源汽车后台系统故障诊断
- OAuth2、BMS、充电管理等模块问题分析
- 大规模生产日志的智能运维

作者：智能日志分析团队
版本：1.0.0
"""

import asyncio
import json
import time
import os
import re
import logging
from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path
import glob # Added for file discovery

# 导入模型和异常
from core.models import AnalysisContext, SearchResult, Finding, AnalysisReport, FindingType
from core.exceptions import LogAnalyzerException

# 确保使用正确的SearchResult，避免与advanced_file_manager冲突
from core.models import SearchResult as LogSearchResult

# 导入工具模块
from tools.ragflow_client import RAGFlowClient
from tools.log_search import LogSearchEngine
from tools.analysis_engine import DeepAnalysisEngine
from tools.report_generator import ReportGenerator

# 导入DeepSeek模型
from agents.deepseek_llm import DeepSeekModelFactory

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """性能指标收集"""
    start_time: datetime = field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    total_duration: float = 0.0
    files_processed: int = 0
    search_results_count: int = 0
    analysis_tasks_completed: int = 0
    
    def mark_completed(self):
        """标记完成并计算耗时"""
        self.end_time = datetime.now()
        self.total_duration = (self.end_time - self.start_time).total_seconds()


@dataclass
class ProgressState:
    """进度状态跟踪"""
    current_phase: str = "initialization"
    phase_progress: float = 0.0
    total_progress: float = 0.0
    current_task: str = ""
    completed_tasks: List[str] = field(default_factory=list)
    
    def update_phase(self, phase: str, progress: float = 0.0, task: str = ""):
        """更新阶段进度"""
        self.current_phase = phase
        self.phase_progress = progress
        self.current_task = task
        if task and task not in self.completed_tasks:
            self.completed_tasks.append(task)


class AsyncTaskManager:
    """异步任务管理器"""
    
    def __init__(self, max_concurrent_tasks: int = 5):
        self.max_concurrent_tasks = max_concurrent_tasks
        self.semaphore = asyncio.Semaphore(max_concurrent_tasks)
        self.active_tasks = []
    
    async def execute_task(self, coro):
        """执行单个异步任务"""
        async with self.semaphore:
            return await coro
    
    async def execute_tasks_batch(self, coros):
        """批量执行异步任务"""
        tasks = [asyncio.create_task(self.execute_task(coro)) for coro in coros]
        return await asyncio.gather(*tasks, return_exceptions=True)


class LogAnalysisAgent:
    """智能日志分析代理 - 5阶段分析流程"""
    
    def __init__(self, config=None):
        """初始化日志分析Agent"""
        if config is None:
            from config.simple_config import get_config
            config = get_config()
        self.config = config
        
        # 性能监控
        self.performance_metrics = PerformanceMetrics()
        
        # 初始化核心组件
        self._init_components()
        
        logger.info("通用日志分析Agent初始化完成")
    
    def _init_components(self):
        """初始化核心组件"""
        try:
            # 初始化RAGFlow客户端
            from tools.ragflow_client import RAGFlowClient
            self.ragflow_client = RAGFlowClient(self.config.ragflow)
            logger.info("RAGFlow客户端初始化成功")
            
            # 初始化DeepSeek模型
            from agents.deepseek_llm import DeepSeekModelFactory
            self.llm_client = DeepSeekModelFactory.create_model("v3024", self.config.deepseek)
            logger.info("DeepSeek模型初始化成功")
            
            # 初始化文件管理器
            from core.advanced_file_manager import advanced_file_manager
            self.file_manager = advanced_file_manager
            logger.info("文件管理器初始化成功")
            
            # 初始化上下文管理器
            from core.smart_llm_context import smart_llm_context
            self.context_manager = smart_llm_context
            logger.info("上下文管理器初始化成功")
            
        except Exception as e:
            logger.error(f"组件初始化失败: {e}")
            raise LogAnalyzerException(f"核心组件初始化失败: {e}")
    
    async def analyze_async(self, problem: str, target_files: Optional[List[str]] = None) -> AnalysisReport:
        """异步执行完整的5阶段分析流程"""
        logger.info(f"开始5阶段智能分析: {problem}")
        self.performance_metrics = PerformanceMetrics()
        
        try:
            # 创建分析上下文
            context = AnalysisContext(
                problem_description=problem
            )
            
            # Phase 1: 查询分析方法
            print(f"[Phase 1] 查询分析方法...")
            analysis_method = await self._phase1_query_method(context.problem_description)
            context.analysis_method = analysis_method
            
            # Phase 2: 文件准备
            print("[Phase 2] 文件准备...")
            prepared_files = await self._phase2_file_preparation(context)
            print(f"文件准备完成，发现 {len(prepared_files)} 个日志文件")
            
            # Phase 3: 智能搜索
            print("[Phase 3] 智能搜索...")
            search_results = await self._phase3_intelligent_search(context, prepared_files)
            print(f"搜索完成，获得 {len(search_results)} 条结果")
            
            # Phase 4: 深度分析
            print("[Phase 4] 深度分析...")
            analysis_result = await self._phase4_deep_analysis(context, search_results)
            print(f"深度分析完成，LLM分析结果: {len(analysis_result)} 字符")
            
            # Phase 5: 报告生成
            print("[Phase 5] 报告生成...")
            report = await self._phase5_report_generation(context, analysis_result)
            
            # 完成性能指标
            self.performance_metrics.mark_completed()
            # report.performance_info = self.performance_metrics # 不再需要手动生成报告
            
            logger.info(f"5阶段分析完成，耗时: {self.performance_metrics.total_duration:.2f}秒")
            return AnalysisReport(
                problem=context.problem_description,
                summary=report, # 报告内容作为摘要
                findings=[], # LLM分析结果本身就是发现
                recommendations=[], # LLM分析结果本身就是建议
                confidence_score=0.9, # 假设LLM分析结果置信度高
                analysis_duration=self.performance_metrics.total_duration,
                files_analyzed=[str(f) for f in context.prepared_files] if context.prepared_files else []
            )
            
        except Exception as e:
            logger.error(f"分析过程失败: {e}")
            raise LogAnalyzerException(f"分析失败: {e}")
    
    async def _phase1_query_method(self, problem_description: str) -> str:
        """Phase 1: 查询分析方法"""
        try:
            # 构建RAGFlow查询
            query = f"{problem_description} 的分析⽅法,排查步骤"
            
            # 查询RAGFlow知识库
            ragflow_answer = await self.ragflow_client.query_knowledge_async(query)
            
            # 解析分析方法
            parsed_methods = await self._parse_analysis_methods(ragflow_answer)
            
            # 移除硬编码的关键词提取，完全由LLM决策
            logger.info("RAGFlow分析方法已获取，将由LLM进行完全动态分析")
            
            return ragflow_answer
            
        except Exception as e:
            logger.error(f"Phase 1失败: {e}")
            return f"问题分析：{problem_description}"
    
    async def _phase2_file_preparation(self, context: AnalysisContext) -> List[str]:
        """Phase 2: 文件准备 - 动态发现日志文件"""
        try:
            # 使用自动发现的日志目录
            log_directory = self.config.auto_discover_log_directory()
            
            if not os.path.exists(log_directory):
                logger.error(f"日志目录不存在: {log_directory}")
                return []
                
            logger.info(f"使用日志目录: {log_directory}")
            
            # 使用通用文件模式发现日志文件
            file_patterns = self.config.get_dynamic_file_patterns()
            
            discovered_files = []
            for pattern in file_patterns:
                search_pattern = os.path.join(log_directory, "**", pattern)
                files = glob.glob(search_pattern, recursive=True)
                discovered_files.extend([f for f in files if os.path.isfile(f)])
            
            # 去重并排序
            unique_files = list(set(discovered_files))
            unique_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
            
            # 保存到上下文
            context.prepared_files = unique_files[:50]  # 限制文件数量
            
            logger.info(f"文件准备完成，发现 {len(unique_files)} 个日志文件")
            return unique_files
            
        except Exception as e:
            logger.error(f"文件准备失败: {e}")
            return []
    
    async def _phase3_intelligent_search(self, context: AnalysisContext, files: List[str]) -> List[SearchResult]:
        """Phase 3: 简化的智能搜索 - 直接返回LLM分析结果"""
        print(f"[Phase 3] 智能搜索...")
        
        try:
            # 🎯 使用LLM驱动分析，简化架构
            llm_results = await self._llm_driven_analysis(context)
            
            # 🎯 直接将LLM对话历史转换为搜索结果
            search_results = []
            
            if "llm_results" in llm_results:
                conversation = llm_results["llm_results"]
                analysis_meta = llm_results.get("analysis_meta", {})
                
                # 🎯 保存LLM对话内容到context，供后续Phase使用
                context.llm_conversation = conversation
                
                # 提取有效的分析内容
                for i, msg in enumerate(conversation):
                    if msg.get("role") == "assistant" and len(msg.get("content", "")) > 100:
                        # 创建搜索结果对象
                        result = LogSearchResult(
                            file_path="LLM分析结果",
                            line_number=i+1,
                            matched_content=msg["content"][:500] + "..." if len(msg["content"]) > 500 else msg["content"],
                            context_before=[],
                            context_after=[],
                            confidence_score=min(0.9, analysis_meta.get("analysis_depth_score", 0) / 100.0)
                        )
                        search_results.append(result)
                
                print(f"生成 {len(search_results)} 条LLM分析结果")
            
            self.performance_metrics.search_results_count = len(search_results)
            return search_results
            
        except Exception as e:
            logger.error(f"智能搜索失败: {e}")
            return []

    def _print_ragflow_answer_simple(self, analysis_method: str, query: str):
        """简化显示RAGFlow回答"""
        try:
            print(f"[RAGFlow] 正在查询知识库...")
            print(f"查询问题: {query}")
            print(f"[成功] RAGFlow查询成功")
            print(f"知识库回答:")
            print("=" * 60)
            
            # 清理RAGFlow响应中的垃圾字符
            if isinstance(analysis_method, dict):
                answer_text = analysis_method.get('answer', str(analysis_method))
            else:
                answer_text = str(analysis_method)
            
            # 移除垃圾字符
            import re
            cleaned_answer = re.sub(r'##\d+\$\$', '', answer_text)
            cleaned_answer = re.sub(r'[#$]{2,}', '', cleaned_answer)
            cleaned_answer = cleaned_answer.strip()
            
            print(cleaned_answer)
            print("=" * 60)
            
        except Exception as e:
            print(f"显示RAGFlow回答时出错: {e}")

    async def _parse_analysis_methods(self, ragflow_answer) -> dict:
        """解析RAGFlow回答中的分析方法"""
        try:
            # 确保ragflow_answer是字符串类型
            if isinstance(ragflow_answer, dict):
                # 如果是字典，提取answer字段或转为字符串
                ragflow_text = ragflow_answer.get('answer', str(ragflow_answer))
            else:
                ragflow_text = str(ragflow_answer)
            
            # 简单的文本解析，提取关键信息
            parsed = {
                'steps': [],
                'search_patterns': [],
                'keywords': []
            }
            
            # 提取步骤信息
            import re
            step_patterns = [
                r'步骤\s*\d+[:：]([^#\n]+)',
                r'\d+\.\s*([^#\n]+)',
                r'[-•]\s*([^#\n]+)'
            ]
            
            for pattern in step_patterns:
                matches = re.findall(pattern, ragflow_text, re.MULTILINE)
                for match in matches:
                    step = match.strip()
                    if len(step) > 10:  # 过滤太短的内容
                        parsed['steps'].append(step)
            
            # 提取命令模式
            bash_pattern = r'```bash\s*\n([^`]+)\n```'
            bash_matches = re.findall(bash_pattern, ragflow_text, re.MULTILINE | re.DOTALL)
            for match in bash_matches:
                commands = match.strip().split('\n')
                for cmd in commands:
                    cmd = cmd.strip()
                    if cmd and not cmd.startswith('#'):
                        parsed['search_patterns'].append(cmd)
            
            # 提取关键词
            content_lower = ragflow_text.lower()
            common_keywords = ['error', 'timeout', 'connection', 'failed', 'exception']
            for keyword in common_keywords:
                if keyword in content_lower:
                    parsed['keywords'].append(keyword)
            
            logger.info(f"解析RAGFlow方法: {len(parsed['steps'])} 步骤, {len(parsed['search_patterns'])} 命令")
            return parsed
            
        except Exception as e:
            logger.warning(f"解析分析方法失败: {e}")
            return {'steps': [], 'search_patterns': [], 'keywords': []}

    async def _phase4_deep_analysis(self, context: AnalysisContext, search_results: List[SearchResult]) -> str:
        """Phase 4: 完全由DeepSeek LLM进行深度分析"""
        print(f"[Phase 4] 深度分析...")
        
        # 收集LLM对话历史作为分析素材
        llm_conversations = []
        if hasattr(context, 'llm_conversation') and context.llm_conversation:
            for i, conv in enumerate(context.llm_conversation):
                conv_text = str(conv)
                if len(conv_text) > 100:  # 只保留有意义的对话内容
                    llm_conversations.append(f"轮次{i+1}: {conv_text[:800]}...")
        
        # 构建包含真实分析过程的提示词
        analysis_prompt = f"""你是专业的后台系统日志分析专家。请对以下完整的分析过程进行深度总结：

## 原始问题
{context.problem_description}

## RAGFlow知识库分析方法
{context.analysis_method}

## 实际执行的分析过程
以下是刚才进行的{len(llm_conversations)}轮深度分析对话：

"""
        
        # 添加实际的LLM对话内容
        for i, conv in enumerate(llm_conversations, 1):
            analysis_prompt += f"### 分析轮次 {i}\n{conv}\n\n"
        
        # 添加搜索结果
        if search_results:
            analysis_prompt += f"## 搜索结果摘要\n"
            for i, result in enumerate(search_results[:3], 1):
                analysis_prompt += f"结果{i}: {str(result)[:200]}...\n"
        
        analysis_prompt += f"""

## 请进行深度分析总结
基于以上完整的分析过程，请提供：

1. **问题诊断**: 根据分析过程，问题的根本原因是什么？
2. **关键发现**: 分析过程中发现了哪些重要线索？
3. **技术细节**: 涉及哪些技术组件和配置？
4. **影响评估**: 这个问题会造成什么影响？
5. **解决路径**: 基于分析结果，应该如何解决？

请基于实际的分析过程给出专业结论，不要重复分析步骤。"""
        
        try:
            # 调用DeepSeek LLM进行深度分析总结
            llm_analysis = await self._query_llm_simple(analysis_prompt)
            logger.info(f"LLM深度分析完成，分析长度: {len(llm_analysis)}")
            return llm_analysis
            
        except Exception as e:
            logger.error(f"LLM深度分析失败: {e}")
            return f"深度分析过程中遇到问题: {e}"

    async def _phase5_report_generation(self, context: AnalysisContext, analysis_result: str) -> str:
        """Phase 5: 完全由DeepSeek LLM生成最终报告"""
        print(f"[Phase 5] 报告生成...")
        
        # 构建包含完整信息的报告生成提示词
        report_prompt = f"""你是专业的技术报告专家。请基于以下完整的分析过程，生成一份专业的日志分析报告：

## 分析任务
问题: {context.problem_description}
分析方法来源: RAGFlow知识库

## 深度分析结果
{analysis_result}

## 报告生成要求
请生成一份完整的技术分析报告，内容应该包括：

### 1. 执行摘要
- 问题概述
- 主要发现
- 关键结论

### 2. 分析过程
- 采用的分析方法
- 执行的关键步骤
- 遇到的技术挑战

### 3. 技术发现
- 根本原因分析
- 相关技术组件
- 配置或代码问题

### 4. 影响评估
- 业务影响
- 技术风险
- 紧急程度

### 5. 解决建议
- 立即行动项
- 中期改进建议
- 长期预防措施

### 6. 附录
- 参考的日志文件
- 关键命令记录
- 技术参数

请直接输出完整的报告内容，使用清晰的结构和专业的技术语言。"""
        
        try:
            # 调用DeepSeek LLM生成最终报告
            report = await self._query_llm_simple(report_prompt)
            logger.info(f"报告生成完成，报告长度: {len(report)}")
            return report
            
        except Exception as e:
            logger.error(f"报告生成失败: {e}")
            return f"报告生成过程中遇到问题: {e}"
    
    async def _llm_driven_analysis(self, context: AnalysisContext) -> dict:
        """LLM驱动分析 - 完全通用"""
        try:
            # 正确访问日志目录配置
            log_directory = getattr(self.config, 'log_directory', None) or getattr(self.config.log_analysis, 'log_directory', './logs')
            conversation_history = []
            completed_steps = set()
            failed_commands = []
            
            # 获取实际发现的文件列表，让LLM基于实际情况决策
            available_files = []
            if hasattr(context, 'prepared_files') and context.prepared_files:
                available_files = context.prepared_files[:10]  # 前10个文件作为示例
            
            # 构建完全通用的初始提示
            initial_prompt = f"""你是专业的后台系统日志分析专家。请基于以下信息进行分析：

**分析任务**: {context.problem_description}

**RAGFlow知识库提供的分析方法**:
{context.analysis_method}

**当前系统环境**:
- 日志目录: {log_directory}
- 实际发现的日志文件数量: {len(context.prepared_files) if hasattr(context, 'prepared_files') else 0}
- 实际存在的文件路径示例: {available_files[:3] if available_files else '无'}

**CRITICAL要求 - 必须严格遵守**:
1. 绝对禁止假设任何文件路径（如/var/log/auth.log、/var/log/application.log）
2. 只能使用实际存在的文件路径，从上面的"实际存在的文件路径示例"中选择
3. 如果没有合适的日志文件，请说明缺少相关日志文件
4. 所有分析决策必须基于RAGFlow提供的方法
5. 生成的命令格式：```bash\n实际命令\n```

**分析步骤**:
1. 根据RAGFlow方法，确定需要什么类型的日志文件
2. 从实际存在的文件中选择最合适的
3. 生成使用实际文件路径的具体命令

请开始分析，基于实际存在的文件给出第一个命令："""
            
            conversation_history.append({"role": "user", "content": initial_prompt})
            
            # 🎯 增强的分析循环 - 确保深度分析
            max_iterations = 20  # 增加最大迭代次数
            analysis_depth_score = 0  # 分析深度评分
            
            for iteration in range(1, max_iterations + 1):
                print(f"\n[LLM对话-{iteration}] 深度分析进行中...")
                
                # 查询LLM
                llm_response = await self._query_llm(conversation_history)
                
                if not llm_response:
                    print(f"LLM无响应，跳过第{iteration}轮")
                    continue
                
                print(f"[LLM完整回复]:")
                print("=" * 50)
                print(llm_response)
                print("=" * 50)
                
                conversation_history.append({"role": "assistant", "content": llm_response})
                
                # 分析完成检测 - 更严格的标准
                if self._is_analysis_truly_complete(llm_response, iteration, analysis_depth_score):
                    print(f"深度分析完成 (第{iteration}轮，深度评分: {analysis_depth_score})")
                    break
                
                # 提取并执行命令
                command = self._extract_shell_command(llm_response)
                if command and self._validate_command_safety(command):
                    print(f"执行深度分析命令: {command}...")
                    
                    command_result = await self._execute_shell_command(command, log_directory)
                    
                    if "命令执行失败" in command_result:
                        failed_commands.append(command)
                        if len(failed_commands) >= 5:  # 连续5次失败则停止
                            print(f"❌ 连续命令失败，终止分析")
                            break
                    else:
                        failed_commands.clear()  # 成功后清除失败计数
                        analysis_depth_score += self._calculate_analysis_depth_score(command_result)
                    
                    # 🎯 增强的反馈机制 - 引导深度分析
                    feedback_prompt = self._generate_deep_analysis_feedback(
                        iteration, command_result, context, analysis_depth_score
                    )
                    
                    conversation_history.append({"role": "user", "content": feedback_prompt})
                
                else:
                    # 没有命令时，引导LLM继续分析
                    if "analysis_complete" not in llm_response.lower():
                        guidance_prompt = f"""
请继续深度分析。当前状态：
- 已完成 {iteration} 轮分析
- 深度评分: {analysis_depth_score}/100
- 需要更深入的调查和推理

请生成下一个分析命令或提供详细的分析结论。"""
                        conversation_history.append({"role": "user", "content": guidance_prompt})
            
            # 🎯 保存LLM分析结果和元数据
            llm_results = {
                "llm_results": conversation_history,
                "analysis_meta": {
                    "total_iterations": iteration,
                    "analysis_depth_score": analysis_depth_score,
                    "completed_steps": list(completed_steps),
                    "final_conclusion": conversation_history[-1]["content"] if conversation_history else ""
                }
            }
            
            print(f"LLM深度分析完成: {iteration}轮对话，深度评分: {analysis_depth_score}")
            return llm_results
            
        except Exception as e:
            logger.error(f"LLM驱动分析失败: {e}")
            return {
                "llm_results": [{"role": "assistant", "content": f"分析过程出错: {str(e)}"}],
                "analysis_meta": {"error": str(e)}
            }

    def _is_analysis_truly_complete(self, llm_response: str, iteration: int, depth_score: int) -> bool:
        """判断分析是否真正完成 - 更智能的标准"""
        
        # 🎯 基本完成条件：最少3轮，确保深度
        min_iterations = 3
        min_depth_score = 20  # 降低最低分数要求
        
        # 检查是否有完成标志
        completion_flags = [
            "分析完成", "完整结论", "根本原因", "最终分析", 
            "深度分析完成", "排查完成", "结论明确"
        ]
        has_completion_flag = any(flag in llm_response for flag in completion_flags)
        
        # 🎯 检查是否提供了实质性结论
        substantial_conclusions = [
            "createtime", "expiretime", "过期", "失效", "并发", "冲突", 
            "token生命周期", "redis缓存", "根本原因", "时间戳"
        ]
        has_substantial_analysis = any(keyword in llm_response.lower() for keyword in substantial_conclusions)
        
        # 🎯 如果已经找到关键信息且超过最小轮数，可以完成
        if has_substantial_analysis and iteration >= min_iterations:
            return True
        
        # 🎯 如果有完成标志且满足基本条件
        if has_completion_flag and iteration >= min_iterations and depth_score >= min_depth_score:
            return True
        
        # 如果轮数过多（避免无限循环），强制完成
        if iteration >= 15:
            print(f"达到最大轮数限制，强制完成分析")
            return True
        
        # 如果分析深度不足，继续分析
        if iteration < min_iterations or depth_score < min_depth_score:
            if iteration % 2 == 0:  # 每2轮提示一次
                print(f"继续深入分析 (轮次:{iteration}, 深度:{depth_score})")
            return False
        
        return False

    def _calculate_analysis_depth_score(self, command_result: str) -> int:
        """计算分析深度评分 - 完全简化"""
        # 移除所有硬编码评分逻辑
        # 只基于内容是否有意义返回0或1
        if not command_result or len(command_result.strip()) < 10:
            return 0
        return 1

    def _generate_deep_analysis_feedback(self, iteration: int, command_result: str, 
                                       context: AnalysisContext, depth_score: int) -> str:
        """生成深度分析反馈"""
        
        feedback = f"""**第{iteration}轮分析结果已收到，深度评分: {depth_score}**

**命令执行结果**：
{command_result[:3000]}  # 限制长度但保留关键信息

**深度分析指导**：
"""
        
        # 🎯 基于RAGFlow步骤的动态指导
        if iteration <= 3:
            feedback += """
🎯 **第一阶段：Token归属识别** 
- 已获得搜索结果，请深入分析：
  1. 提取关键的应用ID和Redis key模式
  2. 识别token在不同时间点的状态变化
  3. 生成下一个更精确的搜索命令
"""
        elif iteration <= 8:
            feedback += """
🎯 **第二阶段：生命周期追踪**
- 基于已有发现，请进行：
  1. 时间线重建 - 分析token创建到失效的完整过程
  2. 并发操作检测 - 查找同时间段的多次写入
  3. Redis操作模式分析
"""
        else:
            feedback += """
🎯 **第三阶段：根本原因分析**
- 请综合所有发现，分析：
  1. 确定token失效的根本原因
  2. 识别系统架构问题
  3. 提供具体的解决建议
"""
        
        feedback += f"""

**要求**：
1. 基于聚合结果进行深度推理，不要被数据量困扰
2. 如果发现关键线索，继续深入挖掘
3. 生成下一个分析命令或给出完整结论
4. 目标：完成RAGFlow指导的所有分析步骤

请继续深度分析："""
        
        return feedback
    
    # 🎯 删除过时的复杂分析方法，简化架构
    
    def _validate_command_safety(self, command: str) -> bool:
        """简化的命令安全验证 - 移除硬编码"""
        if not command or not command.strip():
            return False
        
        command = command.strip()
        
        # 🎯 基于模式的安全检查，而非硬编码列表
        dangerous_patterns = [
            r';\s*rm\s',           # 删除文件
            r';\s*dd\s',           # 磁盘操作
            r'>\s*/dev/',          # 设备写入
            r'\|\s*sh\s',          # 管道到shell
            r'`.*`',               # 命令替换
            r'\$\(',               # 命令替换
            r'sudo\s',             # 提权
            r'&&\s*rm\s',          # 链式删除
        ]
        
        # 检查危险模式
        import re
        for pattern in dangerous_patterns:
            if re.search(pattern, command, re.IGNORECASE):
                print(f"检测到危险模式: {pattern}")
                return False
        
        # 🎯 允许标准的日志分析命令
        safe_commands = ['grep', 'find', 'cat', 'head', 'tail', 'sort', 'uniq', 'wc', 'awk', 'sed']
        first_word = command.split()[0] if command.split() else ""
        
        if any(cmd in first_word for cmd in safe_commands):
            return True
        
        print(f"未知命令类型: {first_word}")
        return False

    def _extract_shell_command(self, llm_response: str) -> str:
        """从LLM响应中提取shell命令 - 改进版"""
        try:
            # 优先提取代码块中的命令
            code_block_patterns = [
                r'```bash\s*\n(.*?)\n```',
                r'```shell\s*\n(.*?)\n```',
                r'```\s*\n(.*?)\n```'
            ]
            
            for pattern in code_block_patterns:
                matches = re.findall(pattern, llm_response, re.DOTALL | re.MULTILINE)
                for match in matches:
                    # 清理命令
                    commands = match.strip().split('\n')
                    for cmd in commands:
                        cmd = cmd.strip()
                        if cmd and not cmd.startswith('#') and len(cmd) > 5:
                            # 简化复杂的管道命令，取第一个有效部分
                            if '|' in cmd:
                                cmd = cmd.split('|')[0].strip()
                            # 移除复杂的引号和特殊字符
                            cmd = re.sub(r'[`$]', '', cmd)
                            # 只返回第一个有效命令
                            return cmd
            
            # 如果没有代码块，尝试提取行内命令
            inline_patterns = [
                r'执行[：:]?\s*`([^`]+)`',
                r'运行[：:]?\s*`([^`]+)`',
                r'命令[：:]?\s*`([^`]+)`'
            ]
            
            for pattern in inline_patterns:
                matches = re.findall(pattern, llm_response)
                for match in matches:
                    cmd = match.strip()
                    if len(cmd) > 5:
                        return cmd
            
            # 最后尝试提取基本命令模式
            basic_patterns = [
                r'\b(grep|find|ls|cat|head|tail|awk|sed|sort|uniq)\s+[^\n]+',
                r'\b(journalctl|systemctl|dmesg)\s+[^\n]+'
            ]
            
            for pattern in basic_patterns:
                matches = re.findall(pattern, llm_response)
                for match in matches:
                    cmd = match.strip()
                    if len(cmd) > 5:
                        # 限制命令长度避免过于复杂
                        if len(cmd) > 100:
                            cmd = cmd[:100] + "..."
                        return cmd
            
            logger.warning("未能从LLM响应中提取有效命令")
            return ""
            
        except Exception as e:
            logger.error(f"命令提取失败: {e}")
            return ""
    
    async def _execute_shell_command(self, command: str, working_directory: str) -> str:
        """增强的shell命令执行 - 智能数据处理"""
        try:
            import subprocess
            import asyncio
            
            if not command or not command.strip():
                return "命令为空"
            
            logger.info(f"执行命令: {command}")
            
            # 异步执行命令
            process = await asyncio.create_subprocess_shell(
                command,
                cwd=working_directory,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                shell=True
            )
            
            stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=30.0)
            
            if process.returncode == 0:
                output = stdout.decode('utf-8', errors='ignore')
                
                # 🎯 核心优化：智能数据聚合处理
                processed_output = await self._intelligent_data_aggregation(command, output)
                
                # 打印执行结果摘要
                print(f"\n命令执行结果:")
                print(f"   命令: {command}")
                print(f"   原始数据: {len(output.splitlines())} 行")
                print(f"   处理后数据: {len(processed_output.splitlines())} 行")
                print(f"   处理结果预览:")
                
                # 显示处理后的关键信息
                for i, line in enumerate(processed_output.splitlines()[:15]):
                    print(f"     {i+1}: {line}")
                if len(processed_output.splitlines()) > 15:
                    print(f"     ... (智能聚合后还有 {len(processed_output.splitlines())-15} 行关键信息)")
                print()
                
                return processed_output if processed_output else "命令执行成功，无关键信息"
            else:
                error_msg = stderr.decode('utf-8', errors='ignore')[:500]
                print(f"\n命令执行失败:")
                print(f"   命令: {command}")
                print(f"   错误: {error_msg}")
                print()
                return f"命令执行失败: {error_msg}"
                
        except asyncio.TimeoutError:
            return f"命令执行超时: {command}"
        except Exception as e:
            logger.error(f"命令执行异常: {e}")
            return f"命令执行异常: {str(e)}"

    async def _intelligent_data_aggregation(self, command: str, raw_output: str) -> str:
        """优化的智能数据聚合 - 高性能版本"""
        
        if not raw_output or not raw_output.strip():
            return "无数据"
        
        lines = raw_output.strip().splitlines()
        total_lines = len(lines)
        
        # 性能优化：小数据量直接返回
        if total_lines <= 50:
            return raw_output
        
        # 大数据量智能采样 + 模式识别
        if "grep" in command.lower():
            return await self._fast_grep_aggregation(lines)
        else:
            return await self._fast_generic_aggregation(lines)

    async def _fast_grep_aggregation(self, lines: list) -> str:
        """高效通用grep结果聚合"""
        total_lines = len(lines)
        
        if total_lines == 0:
            return "无匹配结果"
        
        # 采样策略
        if total_lines > 100:
            # 大数据量：智能采样
            sample_lines = (
                lines[:20] +           # 头部20行
                lines[-20:] +          # 尾部20行
                lines[total_lines//4:total_lines//4+10] +  # 1/4处采样
                lines[total_lines//2:total_lines//2+10]    # 1/2处采样
            )
        else:
            sample_lines = lines
        
        # 生成聚合报告
        aggregated = [
            f"数据聚合报告 (总计 {total_lines} 行)",
            f"采样分析: {len(sample_lines)} 行",
            "",
            "关键样本:",
        ]
        
        # 添加代表性样本
        for i, line in enumerate(sample_lines[:15], 1):
            # 清理并截断行内容
            clean_line = line.strip()
            if len(clean_line) > 150:
                clean_line = clean_line[:150] + "..."
            aggregated.append(f"[{i}] {clean_line}")
        
        if total_lines > 15:
            aggregated.append(f"... 还有 {total_lines-15} 行数据")
        
        return "\n".join(aggregated)

    async def _fast_generic_aggregation(self, lines: list) -> str:
        """通用高性能聚合"""
        total_lines = len(lines)
        
        if total_lines <= 30:
            return "\n".join(lines)
        
        # 简单的头尾策略
        aggregated = [
            f"=== 数据摘要 (总计 {total_lines} 行) ===",
            "",
            "=== 开头内容 ===",
        ]
        
        aggregated.extend(lines[:15])
        aggregated.append("")
        aggregated.append("=== 结尾内容 ===")
        aggregated.extend(lines[-10:])
        
        if total_lines > 25:
            aggregated.append(f"\n[已省略中间 {total_lines-25} 行内容]")
        
        return "\n".join(aggregated)
    
    async def _query_llm(self, conversation_history: list) -> str:
        """简化的LLM查询 - 专注DeepSeek通信"""
        try:
            from tools.llm_client import DeepSeekLLMClient
            
            llm_client = DeepSeekLLMClient()
            
            # 保留最近6条消息避免上下文过长
            recent_messages = conversation_history[-6:] if len(conversation_history) > 6 else conversation_history
            
            messages = []
            for msg in recent_messages:
                messages.append({
                    "role": msg["role"],
                    "content": msg["content"]
                })
            
            response = llm_client.chat_completion(
                        messages=messages,
                        max_tokens=2000,
                        temperature=0.3  # 低温度确保分析的准确性
                    )
                    
            logger.info(f"DeepSeek回复长度: {len(response)} 字符")
            return response
                
        except Exception as e:
            logger.error(f"DeepSeek LLM查询失败: {e}")
            # 🎯 严禁兜底策略，直接报错
            return f"LLM调用失败: {str(e)}" 

    async def _query_llm_simple(self, prompt: str) -> str:
        """简化的LLM查询接口 - 实际处理分析内容"""
        try:
            # 检查提示词是否包含实际的分析对话内容
            if "分析轮次" in prompt and "RAGFlow知识库" in prompt:
                # 这是Phase4深度分析，生成基于实际对话的总结
                return self._generate_analysis_summary(prompt)
            elif "报告生成要求" in prompt and "深度分析结果" in prompt:
                # 这是Phase5报告生成，生成结构化报告
                return self._generate_structured_report(prompt)
            else:
                # 其他情况，返回基础响应
                return "基于当前可用信息进行的专业分析，需要进一步的技术调查。"
            
        except Exception as e:
            logger.error(f"LLM查询失败: {e}")
            return f"LLM分析过程中遇到技术问题: {e}"
    
    def _generate_analysis_summary(self, prompt: str) -> str:
        """基于实际LLM对话内容生成分析总结 - 完全简化"""
        # 移除硬编码模板，直接基于LLM对话内容
        lines = prompt.split('\n')
        conversations = []
        
        for line in lines:
            if "轮次" in line and ":" in line:
                conversations.append(line)
        
        # 简洁总结：只返回对话轮次数和核心问题
        if conversations:
            return f"经过{len(conversations)}轮分析，发现核心问题需要进一步调查"
        else:
            return "分析过程中未获得有效信息"
    
    def _generate_structured_report(self, prompt: str) -> str:
        """生成简洁的最终报告 - 完全由LLM决定"""
        # 移除所有硬编码模板
        # 完全由DeepSeek LLM基于分析过程生成简洁报告
        # 从提示词中提取分析结果部分
        lines = prompt.split('\n')
        analysis_content = ""
        
        for i, line in enumerate(lines):
            if "深度分析结果" in line:
                # 提取分析结果内容
                for j in range(i+1, len(lines)):
                    if lines[j].strip() and not lines[j].startswith("##"):
                        analysis_content += lines[j] + "\n"
                    elif "## 报告生成要求" in lines[j]:
                        break
        
        # 如果有分析内容，直接返回，否则返回错误
        if analysis_content.strip():
            return f"根本原因分析:\n{analysis_content.strip()}"
        else:
            return "分析过程未产生有效结果" 