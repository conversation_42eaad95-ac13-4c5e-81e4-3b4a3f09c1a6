"""
通用后台日志分析系统配置管理

简化的配置管理，直接使用环境变量，避免复杂的类继承问题。
"""

import os
from pathlib import Path
from typing import List, Optional, Dict, Any
from dotenv import load_dotenv

# 加载环境变量
env_path = Path(__file__).parent.parent.parent / '.env'
if env_path.exists():
    load_dotenv(env_path)

class SimpleConfig:
    """简化的配置类"""
    
    def __init__(self):
        # 应用配置
        self.app_name = os.getenv('APP_NAME', 'universal-log-analyzer')
        self.version = os.getenv('APP_VERSION', '1.0.0')
        self.debug = os.getenv('DEBUG', 'false').lower() == 'true'
        self.log_level = os.getenv('LOG_LEVEL', 'INFO')
        
        # DeepSeek配置
        self.deepseek = self._init_deepseek_config()
        
        # RAGFlow配置
        self.ragflow = self._init_ragflow_config()
        
        # 日志分析配置
        self.log_analysis = self._init_log_analysis_config()
    
    def _init_deepseek_config(self):
        """初始化DeepSeek配置"""
        config = type('DeepSeekConfig', (), {})()
        config.api_base = os.getenv('DEEPSEEK_API_BASE', '')
        config.api_key = os.getenv('DEEPSEEK_API_KEY', '')
        config.model = os.getenv('DEEPSEEK_MODEL', 'deepseek-chat')
        config.temperature = float(os.getenv('DEEPSEEK_TEMPERATURE', '0.3'))
        config.max_tokens = int(os.getenv('DEEPSEEK_MAX_TOKENS', '2000'))
        config.timeout = int(os.getenv('DEEPSEEK_TIMEOUT', '60'))
        config.max_retries = int(os.getenv('DEEPSEEK_MAX_RETRIES', '3'))
        return config
    
    def _init_ragflow_config(self):
        """初始化RAGFlow配置"""
        config = type('RAGFlowConfig', (), {})()
        config.base_url = os.getenv('RAGFLOW_BASE_URL', '')
        config.api_key = os.getenv('RAGFLOW_API_KEY', '')
        config.knowledge_base_id = os.getenv('RAGFLOW_KNOWLEDGE_BASE_ID', '')
        config.user_id = os.getenv('RAGFLOW_USER_ID')
        config.assistant_name = os.getenv('RAGFLOW_ASSISTANT_NAME', '通用后台日志分析专家')
        config.timeout = int(os.getenv('RAGFLOW_TIMEOUT', '60'))
        config.max_retries = int(os.getenv('RAGFLOW_MAX_RETRIES', '3'))
        return config
    
    def _init_log_analysis_config(self):
        """初始化日志分析配置"""
        config = type('LogAnalysisConfig', (), {})()
        config.log_directory = self._get_log_directory()
        config.max_file_size_mb = int(os.getenv('MAX_FILE_SIZE_MB', '100'))
        config.max_search_results = int(os.getenv('MAX_SEARCH_RESULTS', '50'))
        config.analysis_timeout = int(os.getenv('ANALYSIS_TIMEOUT', '300'))
        config.ripgrep_path = os.getenv('RIPGREP_PATH', 'rg')
        return config
    
    def _get_log_directory(self) -> str:
        """动态设置日志目录"""
        env_dir = os.getenv('LOG_DIRECTORY')
        if env_dir:
            return env_dir
        
        # 尝试常见的日志目录位置（通用后台系统）
        possible_paths = [
            os.path.expanduser("~/logs"),
            "./logs",
            "../logs",
            "/var/log",
            "/opt/logs"
        ]
        
        for path in possible_paths:
            if os.path.exists(path) and os.access(path, os.R_OK):
                return os.path.abspath(path)
        
        # 如果都不存在，返回当前目录下的logs
        return os.path.abspath("./logs")
    
    def auto_discover_log_directory(self) -> str:
        """自动发现日志目录 - 完全通用"""
        possible_paths = [
            "../oauth2-log/logs"  # 项目实际日志目录
        ]
        
        for path in possible_paths:
            if os.path.exists(path) and os.access(path, os.R_OK):
                return os.path.abspath(path)
        
        # 如果都不存在，返回当前目录下的logs
        return os.path.abspath("./logs")
    
    def validate_required_settings(self) -> List[str]:
        """验证必需配置并返回错误列表"""
        errors = []
        
        # 验证DeepSeek配置
        if not self.deepseek.api_key:
            errors.append("DEEPSEEK_API_KEY is required")
        if not self.deepseek.api_base:
            errors.append("DEEPSEEK_API_BASE is required")
            
        # 验证RAGFlow配置
        if not self.ragflow.api_key:
            errors.append("RAGFLOW_API_KEY is required")
        if not self.ragflow.base_url:
            errors.append("RAGFLOW_BASE_URL is required")
        if not self.ragflow.knowledge_base_id:
            errors.append("RAGFLOW_KNOWLEDGE_BASE_ID is required")
            
        # 验证日志目录
        if not os.path.exists(self.log_analysis.log_directory):
            errors.append(f"Log directory does not exist: {self.log_analysis.log_directory}")
            
        return errors
    
    def get_dynamic_file_patterns(self) -> List[str]:
        """获取动态文件搜索模式 - 完全通用"""
        return [
            "*.log",
            "*.out", 
            "*.err",
            "*error*",
            "*debug*",
            "*access*",
            "*application*",
            "*service*",
            "*system*",
            "*audit*",
            "*trace*"
        ]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式（隐藏敏感信息）"""
        return {
            "app_name": self.app_name,
            "version": self.version,
            "debug": self.debug,
            "log_level": self.log_level,
            "deepseek": {
                "api_base": self.deepseek.api_base,
                "model": self.deepseek.model,
                "temperature": self.deepseek.temperature,
                "max_tokens": self.deepseek.max_tokens,
                "has_api_key": bool(self.deepseek.api_key)
            },
            "ragflow": {
                "base_url": self.ragflow.base_url,
                "assistant_name": self.ragflow.assistant_name,
                "has_api_key": bool(self.ragflow.api_key),
                "has_knowledge_base_id": bool(self.ragflow.knowledge_base_id)
            },
            "log_analysis": {
                "log_directory": self.log_analysis.log_directory,
                "max_file_size_mb": self.log_analysis.max_file_size_mb,
                "max_search_results": self.log_analysis.max_search_results
            }
        }

# 全局配置实例
_config_instance = None

def get_config() -> SimpleConfig:
    """获取全局配置实例"""
    global _config_instance
    if _config_instance is None:
        _config_instance = SimpleConfig()
    return _config_instance

def reload_config() -> SimpleConfig:
    """重新加载配置"""
    global _config_instance
    _config_instance = SimpleConfig()
    return _config_instance