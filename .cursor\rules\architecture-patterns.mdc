---
description: Architecture patterns and design guidelines for the codebase
---

# Architecture Patterns and Design Guidelines

## Configuration Architecture
- Use Pydantic BaseSettings for type-safe configuration
- Support both environment variables and .env files
- Implement validators for critical configuration values
- Separate concerns: database, API keys, features, performance

## Integration Architecture
- Create client classes for external services (RAGFlow, DeepSeek)
- Implement graceful degradation when services unavailable
- Use optional imports with fallback implementations
- Abstract API differences through unified interfaces

## Error Handling Architecture
- Create domain-specific exception hierarchies
- Implement retry logic with exponential backoff
- Use context managers for resource cleanup
- Log errors with appropriate detail levels

## Asynchronous Processing
- Use async/await for I/O bound operations
- Implement proper timeout handling
- Batch operations to improve efficiency
- Use progress tracking for long-running operations

## Tool Integration Patterns
- Wrap external tools as FunctionTool instances
- Provide both direct and LLM-mediated tool calling
- Implement tool validation and testing
- Cache tool results when appropriate

## Data Flow Architecture
- Use dataclasses for structured data
- Implement clear data transformation pipelines
- Separate data models from business logic
- Validate data at system boundaries

## Testing Architecture
- Test configuration loading independently
- Mock external services for unit tests
- Create integration tests for critical workflows
- Test error conditions and edge cases

## Deployment Considerations
- Support both development and production configurations
- Implement health checks for external dependencies
- Use environment-specific overrides
- Document required external services
