#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用LLM上下文管理器

完全通用的日志分析上下文管理，不假设任何特定业务模块。
所有业务模块识别和分析方法完全由RAGFlow知识库和DeepSeek LLM动态确定。
"""

import re
import json
import time
from typing import List, Dict, Set, Optional, Tuple, Any
from dataclasses import dataclass
from pathlib import Path
import logging

try:
    from .advanced_file_manager import FileInfo, advanced_file_manager
except ImportError:
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from core.advanced_file_manager import FileInfo, advanced_file_manager

logger = logging.getLogger(__name__)

@dataclass
class CommandResult:
    """命令执行结果"""
    command: str
    success: bool
    output: str
    execution_time: float
    error_type: Optional[str] = None

@dataclass
class ContextState:
    """上下文状态"""
    iteration: int
    available_files: List[FileInfo]
    working_directory: str
    successful_commands: List[str]
    failed_commands: List[str]
    discovered_patterns: List[str]
    analysis_keywords: List[str]

class SmartCommandGenerator:
    """通用智能命令生成器"""
    
    def __init__(self):
        # 完全通用的命令模板，不假设任何特定业务模块
        self.command_templates = {
            'file_search': [
                "find {base_path} -name '*{keyword}*' -type f 2>/dev/null | head -5",
                "ls -la {base_path}/*{keyword}* 2>/dev/null",
                "locate {keyword} 2>/dev/null | grep -E '\\.(log|txt|out|err)$' | head -5"
            ],
            'content_search': [
                "grep -r '{keyword}' {file_path} 2>/dev/null | head -10",
                "zgrep '{keyword}' {file_path}* 2>/dev/null | head -10",
                "awk '/{keyword}/' {file_path} 2>/dev/null | head -5"
            ],
            'context_extraction': [
                "grep -A 5 -B 5 '{keyword}' {file_path} 2>/dev/null",
                "sed -n '/{keyword}/,+5p' {file_path} 2>/dev/null",
                "grep -n '{keyword}' {file_path} 2>/dev/null | head -3"
            ]
        }
    
    def generate_smart_commands(self, 
                              context: ContextState, 
                              target_keywords: List[str],
                              max_commands: int = 3) -> List[str]:
        """生成通用智能命令序列"""
        commands = []
        
        # 基于可用文件生成通用命令
        if context.available_files:
            accessible_paths = [f.path for f in context.available_files if f.accessible]
            
            if accessible_paths:
                # 选择前几个可访问的文件
                target_files = accessible_paths[:3]
                
                # 基于关键词生成搜索命令
                for keyword in target_keywords[:2]:  # 限制关键词数量
                    if keyword and len(keyword) > 2:
                        commands.append(f"grep -n '{keyword}' '{target_files[0]}' 2>/dev/null | head -5")
                
                # 通用错误搜索
                commands.append(f"grep -n -E '(error|exception|fail|timeout)' '{target_files[0]}' 2>/dev/null | head -5")
                
            else:
                # 通用文件发现策略
                base_dirs = list(set([str(Path(f.path).parent) for f in context.available_files]))
                if base_dirs:
                    commands.append(f"find '{base_dirs[0]}' -name '*.log' 2>/dev/null | head -5")
                    commands.append(f"ls -la '{base_dirs[0]}'/*.log 2>/dev/null | head -5")
        else:
            # 最基础的通用搜索策略
            commands.extend([
                "find . -name '*.log' -type f 2>/dev/null | head -5",
                "ls -la *.log 2>/dev/null",
                "find ../logs/ -name '*.log' 2>/dev/null | head -3"
            ])
        
        return commands[:max_commands]

class SmartLLMContext:
    """通用智能LLM上下文管理器"""
    
    def __init__(self, max_iterations: int = 5):
        self.max_iterations = max_iterations
        self.command_generator = SmartCommandGenerator()
        self.context_cache: Dict[str, Any] = {}
        
        # 收敛策略配置
        self.convergence_config = {
            'min_iterations': 2,
            'max_failed_attempts': 3,
            'success_threshold': 1,
            'pattern_recognition_threshold': 0.7
        }
        
        logger.info(f"通用LLM上下文管理器初始化，最大迭代: {max_iterations}")
    
    async def prepare_enhanced_context(self, 
                                     problem_description: str,
                                     working_directory: str) -> ContextState:
        """准备通用上下文信息"""
        logger.info("准备通用分析上下文...")
        
        # 通用关键词提取（不假设业务模块）
        analysis_keywords = self._extract_general_keywords(problem_description)
        logger.info(f"提取到通用关键词: {analysis_keywords}")
        
        # 通用文件发现策略
        base_paths = [
            working_directory,
            os.path.abspath('.'),
            '../logs',
            './logs'
        ]
        
        # 使用通用文件模式
        file_patterns = self._generate_universal_patterns(analysis_keywords)
        
        logger.info(f"搜索路径: {base_paths}")
        logger.info(f"搜索模式: {file_patterns}")
        
        search_result = await advanced_file_manager.discover_log_files_async(
            base_paths=base_paths,
            patterns=file_patterns,
            max_preview_size=500
        )
        
        logger.info(f"文件发现结果: {search_result.total_files} 总文件, {len(search_result.accessible_files)} 可访问")
        
        context = ContextState(
            iteration=0,
            available_files=search_result.accessible_files,
            working_directory=working_directory,
            successful_commands=[],
            failed_commands=[],
            discovered_patterns=[],
            analysis_keywords=analysis_keywords
        )
        
        return context
    
    def _extract_general_keywords(self, problem_description: str) -> List[str]:
        """从问题描述中提取通用关键词"""
        keywords = []
        
        # 提取可能的标识符
        identifier_pattern = r'[A-Za-z0-9]{8,}'
        identifiers = re.findall(identifier_pattern, problem_description)
        keywords.extend(identifiers[:3])  # 限制数量
        
        # 提取通用技术词汇
        general_tech_keywords = ['error', 'exception', 'fail', 'timeout', 'connect', 'invalid', 'expire']
        for keyword in general_tech_keywords:
            if keyword.lower() in problem_description.lower():
                keywords.append(keyword)
        
        # 去重并限制数量
        unique_keywords = list(set(keywords))
        return unique_keywords[:6]
    
    def _generate_universal_patterns(self, keywords: List[str]) -> List[str]:
        """生成通用文件搜索模式"""
        patterns = [
            "*.log", 
            "*.out",
            "*.err", 
            "*error*",
            "*debug*",
            "*access*",
            "*application*",
            "*service*",
            "*system*"
        ]
        
        # 基于关键词生成动态模式
        for keyword in keywords:
            if len(keyword) > 3:
                patterns.append(f"*{keyword.lower()}*")
        
        return patterns[:12]  # 限制模式数量
    
    def generate_smart_prompt(self, 
                            context: ContextState, 
                            previous_results: List[CommandResult]) -> str:
        """生成通用智能提示词"""
        
        # 分析执行结果模式
        failed_patterns = self._analyze_failure_patterns(previous_results)
        successful_patterns = self._analyze_success_patterns(previous_results)
        
        # 构建文件上下文信息
        file_context = self._build_file_context(context)
        
        # 构建执行指导
        execution_guidance = self._build_execution_guidance(context, failed_patterns)
        
        prompt = f"""你是专业的后台系统日志分析专家。基于以下上下文信息进行智能分析：

## 当前分析状态
- 迭代轮次: {context.iteration}/{self.max_iterations}
- 分析关键词: {', '.join(context.analysis_keywords)}
- 工作目录: {context.working_directory}

## 可用文件信息
{file_context}

## 执行历史
- 成功命令: {len(context.successful_commands)} 个
- 失败命令: {len(context.failed_commands)} 个
{execution_guidance}

## 通用分析策略
请根据上述信息，执行以下分析流程：

1. **智能文件定位**: 使用已验证可访问的日志文件路径
2. **关键词搜索**: 基于问题描述中的关键信息进行搜索
3. **模式识别**: 识别日志中的异常模式和问题特征

## 输出格式要求
- 如果需要执行命令，直接输出**单个**最有效的命令（使用实际存在的文件路径）
- 如果已找到足够信息，输出: "ANALYSIS_COMPLETE: [详细分析结论]"
- 避免重复已经失败的命令模式

请给出你的下一步操作："""
        
        return prompt
    
    def _build_file_context(self, context: ContextState) -> str:
        """构建文件上下文信息"""
        if not context.available_files:
            return "未发现可用的日志文件"
        
        accessible_files = [f for f in context.available_files if f.accessible]
        
        if not accessible_files:
            return f"发现 {len(context.available_files)} 个候选文件，但均不可访问"
        
        file_info = []
        file_info.append(f"可访问的日志文件 ({len(accessible_files)} 个):")
        
        for i, file in enumerate(accessible_files[:5]):  # 显示前5个
            size_mb = file.size / (1024 * 1024) if file.size > 0 else 0
            file_name = file.path.split('/')[-1]
            
            file_info.append(f"  {i+1}. {file_name} ({size_mb:.1f}MB)")
            file_info.append(f"     路径: {file.path}")
            
            # 显示有意义的预览内容
            if file.preview.strip():
                preview_lines = file.preview.strip().split('\n')[:2]
                for line in preview_lines:
                    if line.strip():
                        file_info.append(f"     预览: {line.strip()[:100]}")
                        break
            
            file_info.append("")
        
        # 添加使用提示
        file_info.append("使用提示:")
        file_info.append("  - 使用 grep 命令搜索关键词")
        file_info.append("  - 使用 head/tail 查看文件开头/结尾")
        file_info.append("  - 注意文件路径中的特殊字符")
        
        return "\n".join(file_info)
    
    def _build_execution_guidance(self, 
                                context: ContextState, 
                                failed_patterns: List[str]) -> str:
        """构建执行指导"""
        guidance = []
        
        if failed_patterns:
            guidance.append(f"避免的失败模式: {', '.join(failed_patterns)}")
        
        if context.iteration >= self.convergence_config['max_failed_attempts']:
            guidance.append("建议: 基于现有信息给出结论，避免更多无效尝试")
        
        if context.successful_commands:
            guidance.append(f"成功模式可借鉴: {context.successful_commands[-1]}")
        
        return "\n".join(guidance) if guidance else ""
    
    def _analyze_failure_patterns(self, results: List[CommandResult]) -> List[str]:
        """分析失败模式"""
        patterns = []
        for result in results:
            if not result.success:
                if "No such file or directory" in result.output:
                    patterns.append("文件路径错误")
                elif "Permission denied" in result.output:
                    patterns.append("权限不足")
                elif result.command.startswith("find /"):
                    patterns.append("全局搜索路径")
        
        return list(set(patterns))
    
    def _analyze_success_patterns(self, results: List[CommandResult]) -> List[str]:
        """分析成功模式"""
        patterns = []
        for result in results:
            if result.success and result.output.strip():
                if result.command.startswith("grep"):
                    patterns.append("grep搜索")
                elif result.command.startswith("find"):
                    patterns.append("find定位")
        
        return list(set(patterns))
    
    def should_continue_iteration(self, 
                                context: ContextState, 
                                latest_result: Optional[CommandResult]) -> Tuple[bool, str]:
        """判断是否应该继续迭代"""
        
        if context.iteration >= self.max_iterations:
            return False, f"达到最大迭代次数 ({self.max_iterations})"
        
        if context.iteration < self.convergence_config['min_iterations']:
            return True, "未达到最小迭代要求"
        
        if len(context.failed_commands) >= self.convergence_config['max_failed_attempts']:
            return False, f"连续失败次数过多 ({len(context.failed_commands)})"
        
        if len(context.successful_commands) >= self.convergence_config['success_threshold']:
            return False, f"已获得足够的成功结果 ({len(context.successful_commands)})"
        
        if latest_result and latest_result.success and latest_result.output.strip():
            return False, "最新命令成功且有有效输出"
        
        return True, "继续搜索更多信息"
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        return {
            'max_iterations': self.max_iterations,
            'convergence_config': self.convergence_config,
            'cache_size': len(self.context_cache)
        }

# 单例实例
smart_llm_context = SmartLLMContext() 