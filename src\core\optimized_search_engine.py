#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高性能优化搜索引擎
集成异步文件管理器、智能LLM上下文管理器和性能监控
目标：减少70%执行时间，提高3-5倍搜索效率
"""

import asyncio
import time
import logging
from typing import List, Dict, Any, Optional, Tuple, AsyncGenerator
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import subprocess
import json
from pathlib import Path

try:
    from .advanced_file_manager import advanced_file_manager, FileInfo, SearchResult as FileSearchResult
    from .smart_llm_context import smart_llm_context, ContextState, CommandResult
    from ..tools.llm_client import llm_client
except ImportError:
    # 处理相对导入问题
    import sys
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(current_dir)
    sys.path.insert(0, parent_dir)
    
    try:
        from core.advanced_file_manager import advanced_file_manager, FileInfo, SearchResult as FileSearchResult
        from core.smart_llm_context import smart_llm_context, ContextState, CommandResult
        from tools.llm_client import llm_client
    except ImportError:
        # 最后尝试：直接从src目录导入
        src_dir = os.path.dirname(parent_dir)
        sys.path.insert(0, src_dir)
        from core.advanced_file_manager import advanced_file_manager, FileInfo, SearchResult as FileSearchResult
        from core.smart_llm_context import smart_llm_context, ContextState, CommandResult
        
        # CLI模式：从真实的LLM客户端导入，不使用Mock
        try:
            from tools.llm_client import llm_client
        except ImportError:
            raise RuntimeError("真实LLM客户端不可用，请检查配置")

logger = logging.getLogger(__name__)

@dataclass
class OptimizedSearchResult:
    """优化的搜索结果"""
    files_found: int
    accessible_files: int
    search_time: float
    llm_iterations: int
    final_analysis: str
    performance_metrics: Dict[str, Any]
    discovered_evidence: List[Dict[str, Any]]

class OptimizedSearchEngine:
    """高性能优化搜索引擎"""
    
    def __init__(self):
        self.performance_start = None
        self.total_metrics = {
            'searches_performed': 0,
            'total_time_saved': 0,
            'average_iterations': 0,
            'cache_hit_rate': 0
        }
        
        logger.info("🚀 高性能搜索引擎初始化")
    
    async def execute_intelligent_search(self, 
                                       problem_description: str,
                                       working_directory: str = ".") -> OptimizedSearchResult:
        """执行智能搜索 - 主入口点"""
        self.performance_start = time.time()
        
        logger.info("🔍 启动智能搜索流程")
        logger.info(f"📝 问题描述: {problem_description}")
        logger.info(f"📁 工作目录: {working_directory}")
        
        try:
            # Phase 1: 异步准备增强上下文
            context = await self._prepare_enhanced_context(problem_description, working_directory)
            
            # Phase 2: 智能LLM驱动分析
            analysis_result = await self._execute_smart_llm_analysis(context, problem_description)
            
            # Phase 3: 性能指标收集
            performance_metrics = await self._collect_performance_metrics()
            
            # 构建最终结果
            total_time = time.time() - self.performance_start
            
            result = OptimizedSearchResult(
                files_found=len(context.available_files),
                accessible_files=len([f for f in context.available_files if f.accessible]),
                search_time=total_time,
                llm_iterations=context.iteration,
                final_analysis=analysis_result['conclusion'],
                performance_metrics=performance_metrics,
                discovered_evidence=analysis_result.get('evidence', [])
            )
            
            # 更新全局指标
            self._update_global_metrics(result)
            
            logger.info(f"✅ 搜索完成，总耗时: {total_time:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"❌ 搜索异常: {e}")
            raise
    
    async def _prepare_enhanced_context(self, 
                                      problem_description: str, 
                                      working_directory: str) -> ContextState:
        """准备增强上下文 - 集成异步文件管理器"""
        logger.info("📋 Phase 1: 准备增强上下文")
        
        phase_start = time.time()
        
        # 使用智能LLM上下文管理器
        context = await smart_llm_context.prepare_enhanced_context(
            problem_description=problem_description,
            working_directory=working_directory
        )
        
        phase_time = time.time() - phase_start
        logger.info(f"✅ 上下文准备完成，耗时: {phase_time:.2f}s")
        logger.info(f"📊 发现文件: {len(context.available_files)} 个")
        logger.info(f"🎯 分析关键词: {context.analysis_keywords}")
        
        return context
    
    async def _execute_smart_llm_analysis(self, 
                                        context: ContextState, 
                                        problem_description: str) -> Dict[str, Any]:
        """执行DeepSeek LLM智能循环对话分析 - RAGFlow方法已获取，现在进行LLM分析"""
        logger.info("🧠 Phase 2: 启动DeepSeek V3-0324 LLM循环分析")
        
        phase_start = time.time()
        analysis_results = []
        command_history = []
        
        # 提取目标token（不硬编码）
        target_token = self._extract_token_from_problem(problem_description)
        logger.info(f"🎯 目标token: {target_token}")
        
        print(f"[DeepSeek LLM] 🤖 启动循环对话分析...")
        print(f"[目标Token] {target_token}")
        print(f"[可用文件] {len([f for f in context.available_files if f.accessible])} 个OAuth2日志文件")
        
        # LLM循环对话分析
        max_iterations = 8
        for iteration in range(1, max_iterations + 1):
            context.iteration = iteration
            
            print(f"\n[DeepSeek对话-{iteration}] 💭 LLM分析进行中...")
            
            # 构建LLM提示词（基于RAGFlow方法和实际情况）
            llm_prompt = self._build_llm_analysis_prompt(
                problem_description, target_token, context, command_history, iteration
            )
            
            # 调用DeepSeek LLM
            try:
                llm_response = await self._call_deepseek_llm(llm_prompt)
                print(f"[LLM完整回复]:")
                print("="*50)
                print(llm_response[:500] + ("..." if len(llm_response) > 500 else ""))
                print("="*50)
                
                # 处理LLM响应
                if llm_response.startswith("ANALYSIS_COMPLETE:"):
                    # LLM分析完成
                    final_conclusion = llm_response.replace("ANALYSIS_COMPLETE:", "").strip()
                    print(f"[分析完成] {final_conclusion[:200]}...")
                    
                    phase_time = time.time() - phase_start
                    return {
                        'conclusion': final_conclusion,
                        'evidence': analysis_results,
                        'iterations': iteration,
                        'analysis_time': phase_time,
                        'target_token': target_token,
                        'llm_convergence': 'completed'
                    }
                
                else:
                    # LLM建议执行命令
                    command = self._extract_command_from_llm_response(llm_response)
                    if command:
                        print(f"[执行命令] {command}")
                        
                        # 执行命令
                        command_result = await self._execute_command_async(command)
                        command_history.append(command_result)
                        
                        # 显示命令结果
                        if command_result.success and command_result.output.strip():
                            print(f"[命令结果] {len(command_result.output)} 字符")
                            print("命令输出:")
                            print("-" * 40)
                            print(command_result.output[:300] + ("..." if len(command_result.output) > 300 else ""))
                            print("-" * 40)
                            
                            # 检查是否找到关键证据
                            if target_token in command_result.output:
                                analysis_results.append({
                                    'command': command,
                                    'output': command_result.output,
                                    'timestamp': time.time(),
                                    'relevance': 'high',
                                    'iteration': iteration
                                })
                            
                            context.successful_commands.append(command)
                        else:
                            print(f"[命令结果] 命令执行失败: {command_result.output[:100]}")
                            context.failed_commands.append(command)
                        
                        # 为下一轮LLM提供反馈
                        feedback_prompt = self._build_llm_feedback_prompt(command_result, target_token)
                        print(f"[反馈给LLM的提示]:")
                        print("~" * 40)
                        print(feedback_prompt[:200] + "...")
                        print("~" * 40)
                    else:
                        print(f"[错误] 无法从LLM响应中提取有效命令")
                        break
                
            except Exception as e:
                logger.warning(f"DeepSeek LLM调用失败: {e}")
                print(f"[LLM错误] {str(e)}")
                break
        
        # 迭代结束，生成基于证据的结论
        if analysis_results:
            print(f"\n[最终分析] 基于 {len(analysis_results)} 个证据生成结论...")
            key_findings = self._extract_key_findings(analysis_results, target_token, problem_description)
            conclusion = self._generate_evidence_based_conclusion(
                problem_description, key_findings, analysis_results
            )
        else:
            conclusion = f"基于DeepSeek LLM分析和RAGFlow方法，对token {target_token} 需要更多日志数据验证。"
        
        phase_time = time.time() - phase_start
        logger.info(f"✅ DeepSeek LLM分析完成，耗时: {phase_time:.2f}s，证据: {len(analysis_results)} 个")
        
        return {
            'conclusion': conclusion,
            'evidence': analysis_results,
            'iterations': context.iteration,
            'analysis_time': phase_time,
            'target_token': target_token,
            'llm_convergence': 'max_iterations_reached'
        }
    
    async def _call_llm_async(self, prompt: str) -> str:
        """异步调用LLM"""
        try:
            # 使用线程池执行同步LLM调用
            loop = asyncio.get_event_loop()
            with ThreadPoolExecutor() as executor:
                response = await loop.run_in_executor(
                    executor,
                    lambda: llm_client.chat_completion(
                        messages=[{"role": "user", "content": prompt}],
                        max_tokens=1000,
                        temperature=0.3
                    )
                )
            return response
        except Exception as e:
            logger.error(f"LLM调用异常: {e}")
            return "ANALYSIS_COMPLETE: LLM调用失败，无法继续分析"
    
    async def _execute_command_async(self, command: str) -> CommandResult:
        """异步执行命令"""
        start_time = time.time()
        
        try:
            # 清理和验证命令
            clean_command = self._sanitize_command(command)
            
            if not clean_command:
                return CommandResult(
                    command=command,
                    success=False,
                    output="命令格式无效",
                    execution_time=0,
                    error_type="format_error"
                )
            
            logger.info(f"⚡ 执行命令: {clean_command}")
            
            # 异步执行命令
            process = await asyncio.create_subprocess_shell(
                clean_command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd="."
            )
            
            stdout, stderr = await process.communicate()
            
            execution_time = time.time() - start_time
            success = process.returncode == 0
            
            output = stdout.decode('utf-8', errors='ignore') if stdout else ""
            error = stderr.decode('utf-8', errors='ignore') if stderr else ""
            
            if not success and error:
                output = f"命令执行失败: {error}"
            
            logger.info(f"📊 命令结果: {'成功' if success else '失败'}, 耗时: {execution_time:.2f}s")
            
            return CommandResult(
                command=clean_command,
                success=success,
                output=output.strip(),
                execution_time=execution_time,
                error_type=None if success else "execution_error"
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.warning(f"命令执行异常: {e}")
            
            return CommandResult(
                command=command,
                success=False,
                output=f"执行异常: {str(e)}",
                execution_time=execution_time,
                error_type="exception"
            )
    
    def _sanitize_command(self, command: str) -> str:
        """清理和验证命令"""
        if not command or not command.strip():
            return ""
        
        # 移除多余空白
        clean_command = " ".join(command.strip().split())
        
        # 基本安全检查（避免危险命令）
        dangerous_patterns = ['rm -rf', 'sudo rm', 'format', '> /dev/', 'dd if=']
        for pattern in dangerous_patterns:
            if pattern in clean_command.lower():
                logger.warning(f"检测到危险命令模式: {pattern}")
                return ""
        
        return clean_command
    
    async def _generate_final_conclusion(self, 
                                       problem_description: str,
                                       evidence: List[Dict[str, Any]],
                                       termination_reason: str) -> str:
        """生成最终结论"""
        
        if not evidence:
            return f"基于知识库分析，{problem_description} 的问题分析已完成。由于环境限制({termination_reason})，建议查看RAGFlow知识库中的专业指导进行进一步排查。"
        
        # 基于证据生成结论
        evidence_summary = []
        for item in evidence:
            if item['output']:
                evidence_summary.append(f"命令 '{item['command']}' 发现: {item['output'][:200]}")
        
        if evidence_summary:
            conclusion = f"分析发现以下证据：\n" + "\n".join(evidence_summary[:3])
            conclusion += f"\n\n基于上述发现，建议进一步查看相关日志文件进行详细分析。"
        else:
            conclusion = f"分析过程中执行了多个搜索命令，但未发现明确的问题证据。建议按照RAGFlow知识库中的专业指导进行系统性排查。"
        
        return conclusion
    
    async def _collect_performance_metrics(self) -> Dict[str, Any]:
        """收集性能指标"""
        
        # 从各个组件收集指标
        file_manager_stats = advanced_file_manager.get_performance_stats()
        llm_context_stats = smart_llm_context.get_performance_metrics()
        
        total_time = time.time() - self.performance_start if self.performance_start else 0
        
        return {
            'total_execution_time': total_time,
            'file_manager_performance': file_manager_stats,
            'llm_context_performance': llm_context_stats,
            'estimated_time_saved': max(0, 120 - total_time),  # 与原始121秒对比
            'optimization_ratio': max(0, (121 - total_time) / 121 * 100) if total_time < 121 else 0
        }
    
    def _update_global_metrics(self, result: OptimizedSearchResult):
        """更新全局性能指标"""
        self.total_metrics['searches_performed'] += 1
        self.total_metrics['total_time_saved'] += result.performance_metrics.get('estimated_time_saved', 0)
        
        # 计算平均迭代次数
        current_avg = self.total_metrics['average_iterations']
        n = self.total_metrics['searches_performed']
        self.total_metrics['average_iterations'] = (current_avg * (n-1) + result.llm_iterations) / n
    
    def get_optimization_report(self) -> Dict[str, Any]:
        """获取优化效果报告"""
        return {
            'total_searches': self.total_metrics['searches_performed'],
            'total_time_saved': self.total_metrics['total_time_saved'],
            'average_iterations': round(self.total_metrics['average_iterations'], 1),
            'average_time_saved_per_search': (
                self.total_metrics['total_time_saved'] / 
                max(1, self.total_metrics['searches_performed'])
            ),
            'optimization_summary': self._generate_optimization_summary()
        }
    
    def _generate_optimization_summary(self) -> str:
        """生成优化效果摘要"""
        if self.total_metrics['searches_performed'] == 0:
            return "暂无搜索数据"
        
        avg_saved = self.total_metrics['total_time_saved'] / self.total_metrics['searches_performed']
        avg_iterations = self.total_metrics['average_iterations']
        
        summary = f"平均每次搜索节省 {avg_saved:.1f}s，平均迭代 {avg_iterations:.1f} 轮"
        
        if avg_saved > 60:
            summary += " 🚀 性能提升显著！"
        elif avg_saved > 30:
            summary += " ✅ 性能优化良好"
        else:
            summary += " 📈 仍有优化空间"
        
        return summary

# 单例实例
    def _extract_token_from_problem(self, problem_description: str) -> str:
        """从问题描述中提取token（不硬编码）"""
        import re
        
        # 匹配常见的token模式
        token_patterns = [
            r'([A-Za-z0-9]{15,25})',  # 一般token长度
            r'AMA[A-Za-z0-9]+',       # AMA开头的token
            r'token[:\s]+([A-Za-z0-9]+)',  # token: 格式
        ]
        
        for pattern in token_patterns:
            matches = re.findall(pattern, problem_description)
            if matches:
                return matches[0] if isinstance(matches[0], str) else matches[0][0]
        
        # 如果没有找到，返回问题中的第一个长字符串
        words = re.findall(r'[A-Za-z0-9]{8,}', problem_description)
        return words[0] if words else "unknown_token"
    
    def _generate_problem_specific_commands(self, problem: str, available_files: List[FileInfo], target_token: str) -> List[str]:
        """基于实际问题和文件生成专业命令（严禁硬编码）"""
        commands = []
        
        # 获取可访问的OAuth2日志文件
        accessible_files = [f for f in available_files if f.accessible and 'oauth2' in f.path.lower()]
        if not accessible_files:
            accessible_files = [f for f in available_files if f.accessible][:3]
        
        if not accessible_files:
            return ["echo '没有可用的日志文件'"]
        
        main_file = accessible_files[0].path
        
        # 基于RAGFlow方法和实际问题生成命令
        commands.extend([
            f"grep -n '{target_token}' '{main_file}' 2>/dev/null | head -5",
            f"grep -n 'auth:token:app:' '{main_file}' 2>/dev/null | grep '{target_token}' | head -3",
            f"grep -A 3 -B 3 '{target_token}' '{main_file}' 2>/dev/null | head -10",
            f"grep -n 'Data saved to Redis' '{main_file}' 2>/dev/null | head -5",
        ])
        
        # 如果有多个文件，搜索其他文件
        if len(accessible_files) > 1:
            second_file = accessible_files[1].path
            commands.append(f"grep -n '{target_token}' '{second_file}' 2>/dev/null | head -3")
        
        return commands[:5]  # 限制命令数量
    
    def _extract_key_findings(self, analysis_results: List[Dict], target_token: str, problem: str) -> Dict[str, Any]:
        """从实际分析结果中提取关键发现（不硬编码）"""
        findings = {
            'token_found': False,
            'app_id': None,
            'timestamps': [],
            'redis_operations': [],
            'error_patterns': [],
            'total_evidence': len(analysis_results)
        }
        
        for result in analysis_results:
            output = result['output']
            
            # 检查是否找到目标token
            if target_token in output:
                findings['token_found'] = True
            
            # 提取应用ID模式
            import re
            app_id_matches = re.findall(r'auth:token:app:(\d+)', output)
            if app_id_matches and not findings['app_id']:
                findings['app_id'] = app_id_matches[0]
            
            # 提取时间戳
            time_matches = re.findall(r'2025-\d{2}-\d{2}[\s_]\d{2}:\d{2}:\d{2}', output)
            findings['timestamps'].extend(time_matches[:3])
            
            # 检查Redis操作
            if 'Data saved to Redis' in output:
                findings['redis_operations'].append(output[:200])
            
            # 检查错误模式
            if any(err in output.lower() for err in ['error', 'fail', 'invalid', 'expired']):
                findings['error_patterns'].append(output[:150])
        
        return findings
    
    def _generate_evidence_based_conclusion(self, problem: str, findings: Dict, evidence: List[Dict]) -> str:
        """基于实际证据生成结论（严格禁止硬编码）"""
        target_token = self._extract_token_from_problem(problem)
        
        conclusion_parts = []
        conclusion_parts.append(f"基于RAGFlow知识库指导和实际日志分析，关于token {target_token} 的问题：")
        
        if findings['token_found']:
            conclusion_parts.append(f"✅ 在日志中成功定位到目标token {target_token}")
            
            if findings['app_id']:
                conclusion_parts.append(f"🔍 识别到应用ID: {findings['app_id']}")
            
            if findings['timestamps']:
                conclusion_parts.append(f"⏰ 发现相关时间点: {', '.join(findings['timestamps'][:2])}")
            
            if findings['redis_operations']:
                conclusion_parts.append(f"💾 检测到 {len(findings['redis_operations'])} 个Redis操作记录")
            
            if findings['error_patterns']:
                conclusion_parts.append(f"⚠️ 发现 {len(findings['error_patterns'])} 个潜在错误模式")
                
            # 根据发现的证据推断原因
            if len(findings['redis_operations']) > 1:
                conclusion_parts.append("🔄 分析表明可能存在token覆盖或并发操作问题")
            
        else:
            conclusion_parts.append(f"❌ 在当前日志文件中未找到token {target_token}")
            conclusion_parts.append("建议: 1) 检查其他日志文件 2) 确认token格式 3) 扩大时间范围")
        
        conclusion_parts.append(f"📊 分析基于 {findings['total_evidence']} 个实际证据，遵循RAGFlow专业方法论")
        
        return " ".join(conclusion_parts)
    
    def _build_llm_analysis_prompt(self, problem: str, target_token: str, context: ContextState, 
                                   command_history: List, iteration: int) -> str:
        """构建DeepSeek LLM分析提示词（基于RAGFlow方法）"""
        
        # 获取可用文件信息
        accessible_files = [f for f in context.available_files if f.accessible]
        file_list = "\n".join([f"  - {f.path.split('/')[-1]} ({f.size/1024/1024:.1f}MB)" 
                              for f in accessible_files[:5]])
        
        # 历史命令总结
        if command_history:
            last_result = command_history[-1]
            history_summary = f"上一个命令: {last_result.command}\n结果: {'成功' if last_result.success else '失败'}"
            if last_result.success and last_result.output:
                history_summary += f"\n输出: {last_result.output[:200]}..."
        else:
            history_summary = "这是第一轮分析"
        
        prompt = f"""你是一个专业的OAuth2日志分析专家，使用DeepSeek V3-0324模型进行智能分析。

## 当前任务
分析问题: {problem}
目标Token: {target_token}
分析轮次: {iteration}/8

## 可用OAuth2日志文件
{file_list}

## 分析历史
{history_summary}

## RAGFlow知识库方法指导
基于知识库，Token无效分析的关键步骤：
1. Token归属识别: 搜索 auth:token:app:{{应用ID}} 格式
2. 生命周期追踪: 检查创建时间和有效期
3. 并发冲突检测: 分析毫秒级间隔的多次写入操作

## 智能分析要求
请基于上述信息，决定下一步操作：

1. 如果需要执行命令搜索日志，直接输出命令（如: grep -n '{target_token}' [文件路径]）
2. 如果已找到足够证据，输出: "ANALYSIS_COMPLETE: [详细分析结论]"

重要原则:
- 使用实际的文件路径，不要用application.log
- 基于实际发现的证据进行分析
- 遵循RAGFlow专业方法论
- 每次只输出一个最有效的命令

请给出你的分析决策:"""
        
        return prompt
    
    async def _call_deepseek_llm(self, prompt: str) -> str:
        """调用真实的LLM客户端进行分析（CLI模式）"""
        try:
            # 直接使用系统中的LLM客户端
            response = llm_client.chat_completion(
                messages=[
                    {"role": "system", "content": "你是专业的OAuth2日志分析专家，使用DeepSeek模型进行分析。"},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=2000,
                temperature=0.3
            )
            return response
                
        except Exception as e:
            logger.error(f"真实LLM调用失败: {e}")
            # CLI模式下抛出异常，不使用模拟
            raise RuntimeError(f"LLM客户端不可用: {e}")
    

    
    def _extract_command_from_llm_response(self, response: str) -> str:
        """从LLM响应中提取命令"""
        lines = response.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            # 检查是否是命令（以常见命令开头）
            if (line.startswith('grep ') or 
                line.startswith('find ') or 
                line.startswith('cat ') or 
                line.startswith('ls ') or
                line.startswith('awk ') or
                line.startswith('sed ')):
                return line
        
        # 如果没有明确的命令，返回整个响应作为可能的命令
        clean_response = response.strip()
        if not clean_response.startswith("ANALYSIS_COMPLETE:"):
            return clean_response
        
        return ""
    
    def _build_llm_feedback_prompt(self, command_result, target_token: str) -> str:
        """构建给LLM的反馈提示"""
        
        if command_result.success:
            if command_result.output.strip():
                # 检查是否找到目标token
                if target_token in command_result.output:
                    feedback = f"命令执行成功！发现目标token {target_token}，输出长度: {len(command_result.output)} 字符"
                else:
                    feedback = f"命令执行成功，但未发现目标token {target_token}，输出长度: {len(command_result.output)} 字符"
                
                # 显示部分输出用于LLM分析
                output_preview = command_result.output[:200] + ("..." if len(command_result.output) > 200 else "")
                feedback += f"\n输出预览: {output_preview}"
            else:
                feedback = "命令执行成功，但无输出内容"
        else:
            feedback = f"命令执行失败: {command_result.output[:100]}"
        
        return f"""
                    命令执行结果：
                    {feedback}
                    
                    请分析这个结果，决定下一步操作：
                    1. 如果需要读取特定文件的内容，请说"READ_FILE: 文件路径 行号范围"
                    2. 如果需要执行其他命令，请直接说出命令
                    3. 如果已经找到问题根因，请说"ANALYSIS_COMPLETE: 分析结论"
                    """

optimized_search_engine = OptimizedSearchEngine() 