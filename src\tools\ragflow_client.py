#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实RAGFlow客户端实现
基于用户提供的成功验证代码，支持完整的助手管理和知识库查询
"""

import time
import requests
import logging
from typing import Optional, Dict, Any, List
from pathlib import Path

logger = logging.getLogger(__name__)


class RAGFlowClient:
    """真实RAGFlow客户端 - 基于成功验证的实现"""
    
    def __init__(self, config):
        """初始化RAGFlow客户端
        
        Args:
            config: RAGFlow配置对象，包含base_url, api_key, knowledge_base_id等
        """
        # 使用真实的RAGFlow配置
        self.ragflow_base_url = getattr(config, 'base_url', "http://10.13.197.64")
        self.ragflow_api_key = getattr(config, 'api_key', "ragflow-g3Y2U3NmJlNTEyMjExZjA4MjNiM2FhZW")
        self.dataset_id = getattr(config, 'knowledge_base_id', "fb14784c615011f0bb00d23d43f130ad")
        self.assistant_name = getattr(config, 'assistant_name', "OAuth2分析专家助手")
        self.timeout = getattr(config, 'timeout', 60)
        
        self.ragflow_headers = {
            "Authorization": f"Bearer {self.ragflow_api_key}",
            "Content-Type": "application/json"
        }
        
        self.chat_assistant_id = None
        
        logger.info(f"RAGFlow客户端初始化")
        logger.info(f"  Base URL: {self.ragflow_base_url}")
        logger.info(f"  数据集ID: {self.dataset_id}")
        logger.info(f"  助手名称: {self.assistant_name}")
    
    def test_connectivity(self) -> bool:
        """测试RAGFlow连通性"""
        try:
            logger.info("测试RAGFlow API连通性...")
            
            url = f"{self.ragflow_base_url}/api/v1/chats"
            response = requests.get(url, headers=self.ragflow_headers, timeout=10)
            
            logger.info(f"连通性测试状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    assistants = result.get('data', [])
                    logger.info(f"API连通正常，发现 {len(assistants)} 个现有助手")
                    return True
                else:
                    logger.error(f"API响应业务错误: {result.get('message', '未知错误')}")
                    return False
            else:
                logger.error(f"API连通性测试失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"连通性测试异常: {e}")
            return False
    
    def find_or_create_assistant(self) -> bool:
        """查找或创建助手 - 基于成功验证的实现"""
        try:
            # 首先测试连通性
            if not self.test_connectivity():
                logger.error("RAGFlow API连通性测试失败")
                return False
            
            # 列出现有助手
            url = f"{self.ragflow_base_url}/api/v1/chats"
            response = requests.get(url, headers=self.ragflow_headers, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    assistants = result.get('data', [])
                    
                    # 查找现有助手
                    for assistant in assistants:
                        if assistant.get('name') == self.assistant_name:
                            self.chat_assistant_id = assistant.get('id')
                            dataset_ids = assistant.get('dataset_ids', [])
                            
                            logger.info(f"找到现有助手: {self.assistant_name}")
                            logger.info(f"  助手ID: {self.chat_assistant_id}")
                            logger.info(f"  关联数据集: {dataset_ids}")
                            
                            if self.dataset_id in dataset_ids:
                                logger.info("助手已正确配置数据集")
                                return True
                            else:
                                logger.info("助手缺少目标数据集，尝试更新...")
                                return self.update_assistant_datasets()
                    
                    # 创建新助手
                    logger.info(f"未找到现有助手，创建新助手: {self.assistant_name}")
                    return self.create_new_assistant()
                else:
                    logger.error(f"RAGFlow API错误: {result.get('message', '未知错误')}")
                    return False
            else:
                logger.error(f"RAGFlow HTTP错误: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"RAGFlow连接异常: {e}")
            return False
    
    def create_new_assistant(self) -> bool:
        """创建新助手"""
        try:
            url = f"{self.ragflow_base_url}/api/v1/chats"
            payload = {
                "name": self.assistant_name,
                "dataset_ids": [self.dataset_id]
            }
            
            logger.info(f"创建新助手: {self.assistant_name}")
            logger.info(f"  使用数据集: {self.dataset_id}")
            
            response = requests.post(url, headers=self.ragflow_headers, json=payload, timeout=15)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    data = result.get('data', {})
                    self.chat_assistant_id = data.get('id')
                    logger.info(f"助手创建成功，ID: {self.chat_assistant_id}")
                    return True
                else:
                    logger.error(f"创建助手业务错误: {result.get('message', '未知错误')}")
                    return False
            else:
                logger.error(f"创建助手HTTP错误: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"创建助手异常: {e}")
            return False
    
    def update_assistant_datasets(self) -> bool:
        """更新助手数据集"""
        try:
            url = f"{self.ragflow_base_url}/api/v1/chats/{self.chat_assistant_id}"
            payload = {
                "name": self.assistant_name,
                "dataset_ids": [self.dataset_id]
            }
            
            logger.info(f"更新助手数据集: {self.chat_assistant_id}")
            
            response = requests.put(url, headers=self.ragflow_headers, json=payload, timeout=15)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    logger.info("助手数据集更新成功")
                    return True
                else:
                    logger.error(f"更新助手业务错误: {result.get('message', '未知错误')}")
                    return False
            else:
                logger.error(f"更新助手HTTP错误: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"更新助手异常: {e}")
            return False
    
    def create_session(self) -> Optional[str]:
        """创建聊天会话"""
        try:
            if not self.chat_assistant_id:
                logger.error("助手ID不存在，无法创建会话")
                return None
            
            url = f"{self.ragflow_base_url}/api/v1/chats/{self.chat_assistant_id}/sessions"
            session_name = f"日志分析会话_{int(time.time())}"
            payload = {"name": session_name}
            
            logger.info(f"创建会话: {session_name}")
            
            response = requests.post(url, headers=self.ragflow_headers, json=payload, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    session_id = result.get('data', {}).get('id')
                    logger.info(f"会话创建成功，ID: {session_id}")
                    return session_id
                else:
                    logger.error(f"创建会话业务错误: {result.get('message', '未知错误')}")
                    return None
            else:
                logger.error(f"创建会话HTTP错误: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"创建会话异常: {e}")
            return None
    
    def query_knowledge(self, question: str) -> str:
        """查询知识库 - 核心功能实现"""
        try:
            # 确保助手就绪
            if not self.chat_assistant_id:
                logger.info("助手未初始化，尝试创建...")
                if not self.find_or_create_assistant():
                    return "助手创建失败，无法查询知识库"
            
            # 创建会话
            session_id = self.create_session()
            if not session_id:
                return "会话创建失败，无法查询知识库"
            
            # 查询知识库
            url = f"{self.ragflow_base_url}/api/v1/chats/{self.chat_assistant_id}/completions"
            payload = {
                "question": question,
                "stream": False,
                "session_id": session_id
            }
            
            logger.info(f"查询知识库")
            logger.info(f"  问题长度: {len(question)} 字符")
            logger.info(f"  超时设置: {self.timeout} 秒")
            
            response = requests.post(url, headers=self.ragflow_headers, json=payload, timeout=self.timeout)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    data = result.get('data', {})
                    answer = data.get('answer', '')
                    
                    if answer:
                        # 清理RAGFlow的引用标记 (如 ##0$$, ##1$$)
                        cleaned_answer = self._clean_ragflow_references(answer)
                        logger.info(f"获得知识库回答: {len(cleaned_answer)} 字符 (已清理引用标记)")
                        return cleaned_answer
                    else:
                        logger.warning("知识库未返回有效答案")
                        return f"未获取到关于'{question}'的专业指导"
                else:
                    error_message = result.get('message', '未知错误')
                    logger.error(f"查询业务错误: {error_message}")
                    return f"RAGFlow API错误: {error_message}"
            else:
                logger.error(f"查询HTTP请求失败: {response.status_code}")
                return f"RAGFlow HTTP错误: {response.status_code}"
                
        except requests.exceptions.ReadTimeout:
            timeout_msg = f"RAGFlow查询超时({self.timeout}秒)"
            logger.error(timeout_msg)
            return timeout_msg
        except Exception as e:
            logger.error(f"查询知识库异常: {e}")
            return f"RAGFlow查询异常: {str(e)}"
    
    async def query_knowledge_async(self, question: str) -> Dict[str, Any]:
        """异步查询知识库 - 返回结构化结果"""
        try:
            answer = self.query_knowledge(question)
            
            return {
                "status": "success" if "失败" not in answer and "异常" not in answer and "错误" not in answer else "error",
                "answer": answer,
                "question": question,
                "assistant_id": self.chat_assistant_id,
                "dataset_id": self.dataset_id
            }
            
        except Exception as e:
            logger.error(f"异步查询异常: {e}")
            return {
                "status": "error",
                "answer": f"异步查询异常: {str(e)}",
                "question": question,
                "assistant_id": self.chat_assistant_id,
                "dataset_id": self.dataset_id
            }
    
    def initialize(self) -> bool:
        """初始化RAGFlow客户端，确保所有组件就绪"""
        try:
            logger.info("初始化RAGFlow客户端...")
            
            # 测试连通性
            if not self.test_connectivity():
                logger.error("RAGFlow连通性测试失败")
                return False
            
            # 查找或创建助手
            if not self.find_or_create_assistant():
                logger.error("助手初始化失败")
                return False
            
            # 验证助手可用性
            if not self.chat_assistant_id:
                logger.error("助手ID为空")
                return False
            
            logger.info("RAGFlow客户端初始化成功")
            logger.info(f"  助手ID: {self.chat_assistant_id}")
            logger.info(f"  数据集ID: {self.dataset_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"初始化异常: {e}")
            return False
    
    def _clean_ragflow_references(self, text: str) -> str:
        """清理RAGFlow返回内容中的引用标记
        
        RAGFlow会在回答中添加引用标记如 ##0$$, ##1$$ 来标识知识库来源，
        这些标记在shell命令执行中会造成问题，需要清理掉。
        
        Args:
            text: 原始RAGFlow回答
            
        Returns:
            清理后的文本
        """
        import re
        if not text:
            return text
        
        # 清理引用标记模式: ##数字$$
        # 这些标记用于标识知识库来源，在shell命令执行中会造成问题
        pattern = r'##\d+\$\$'
        cleaned_text = re.sub(pattern, '', text)
        
        # 清理其他可能的引用格式
        # 清理可能的其他标记格式: #数字#, [数字], (数字) 等
        pattern2 = r'#\d+#'
        cleaned_text = re.sub(pattern2, '', cleaned_text)
        
        # 清理多余的空格和换行
        cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()
        
        logger.debug(f"引用标记清理: {len(text)} -> {len(cleaned_text)} 字符")
        
        return cleaned_text 