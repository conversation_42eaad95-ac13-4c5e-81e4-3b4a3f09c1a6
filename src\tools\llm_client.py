"""
DeepSeek-V3-0324 LLM客户端 - 基于Google ADK兼容性修复
参考: log-agent/deepseek_compatible_litellm.py
核心要求：RAGFlow一次查询 → DeepSeek多轮循环分析
"""

import os
import logging
from typing import Dict, Any, List

logger = logging.getLogger(__name__)

class DeepSeekLLMClient:
    """
    DeepSeek-V3-0324 LLM客户端
    核心修复: role="developer" → role="system" (Google ADK兼容性)
    基于用户强调：充分利用Google ADK特性，DeepSeek-V3-0324模型
    """
    
    def __init__(self):
        # 基于.env文件加载DeepSeek配置
        from dotenv import load_dotenv
        load_dotenv()  # 加载.env文件
        
        self.api_key = os.getenv("DEEPSEEK_API_KEY")
        self.base_url = os.getenv("DEEPSEEK_API_BASE", "https://api.deepseek.com")
        
        # 必须配置API Key，不使用硬编码
        if not self.api_key:
            raise ValueError("必须在.env文件中设置DEEPSEEK_API_KEY")
        
        self.model = os.getenv("DEEPSEEK_MODEL", "deepseek/deepseek-chat")
        logger.info(f"✅ DeepSeek配置加载成功:")
        logger.info(f"   模型: {self.model}")
        logger.info(f"   API Base: {self.base_url}")
        
        # 导入LiteLLM（Google ADK集成）- 必须安装
        import litellm
        self.litellm = litellm
        logger.info("✅ LiteLLM导入成功，支持Google ADK集成")
    
    def _fix_adk_role_compatibility(self, messages: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """
        关键修复：ADK role="developer" → role="system"
        解决Google ADK与DeepSeek的兼容性问题
        用户强调：这个非常重要！
        """
        fixed_messages = []
        
        for msg in messages:
            if msg.get("role") == "developer":
                logger.debug("🔧 修复ADK角色: developer → system")
                fixed_msg = msg.copy()
                fixed_msg["role"] = "system"
                fixed_messages.append(fixed_msg)
            else:
                fixed_messages.append(msg)
        
        return fixed_messages
    
    def chat_completion(
        self, 
        messages: List[Dict[str, str]], 
        max_tokens: int = 2000,
        temperature: float = 0.3  # DeepSeek官方推荐值
    ) -> str:
        """
        DeepSeek-V3-0324聊天完成API
        充分利用Google ADK特性和Function Calling改进
        用户要求：后续都与DeepSeek LLM通信
        """
        try:
            # 应用ADK兼容性修复（用户强调的关键点）
            fixed_messages = self._fix_adk_role_compatibility(messages)
            
            # 只使用真实API调用，无模拟兜底
            response = self.litellm.completion(
                model=self.model,
                messages=fixed_messages,
                max_tokens=max_tokens,
                temperature=temperature,
                api_key=self.api_key,
                api_base=self.base_url
            )
            
            content = response.choices[0].message.content
            logger.info(f"✅ DeepSeek-V3-0324真实响应: {len(content)}字符")
            return content
            
        except Exception as e:
            logger.error(f"DeepSeek-V3-0324调用失败: {e}")
            # 直接抛出异常，不使用兜底策略
            raise RuntimeError(f"LLM调用失败: {e}")
    


# 全局实例（充分利用Google ADK特性）
llm_client = DeepSeekLLMClient() 