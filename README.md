# 智能日志分析器 (Intelligent Log Analyzer)

基于Google ADK框架和DeepSeek-R1模型的企业级智能日志分析系统，专为新能源汽车后台系统设计。

## 🎯 项目特点

- **🤖 智能分析**: 基于DeepSeek-R1模型的智能日志分析和问题诊断
- **📚 知识库驱动**: 通过RAGFlow知识库获取分析方法，支持经验积累
- **🔍 高效搜索**: 基于ripgrep的高性能日志搜索引擎
- **🛠️ 可靠架构**: 使用Google ADK FunctionTool模式，已验证100%兼容性
- **📊 智能报告**: 自动生成详细的分析报告和建议措施
- **🔄 多轮对话**: 支持复杂问题的深入分析和智能决策

## 🏗️ 架构设计

### 核心组件

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Interface │    │  CLI Interface  │    │   API Gateway   │
│   (Streamlit)   │    │   (Argparse)    │    │   (FastAPI)     │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
        ┌─────────────────────────▼─────────────────────────┐
        │            LogAnalysisAgent (ADK)                │
        │  - DeepSeek-R1 LLM                              │
        │  - Session Management                           │
        │  - Multi-turn Dialogue                          │
        └─────────────────────┬───────────────────────────┘
                              │
          ┌───────────────────┼───────────────────┐
          │                   │                   │
    ┌─────▼──────┐    ┌──────▼──────┐    ┌──────▼──────┐
    │ RAGFlow    │    │ FunctionTool│    │  Log Engine │
    │ Knowledge  │    │   Toolkit   │    │   (ripgrep) │
    │    Base    │    │             │    │             │
    └────────────┘    └─────────────┘    └─────────────┘
```

### 技术栈

- **LLM框架**: Google ADK Python + DeepSeek-R1
- **搜索引擎**: ripgrep + 自定义索引
- **知识库**: RAGFlow API
- **数据处理**: pandas + 自定义解析器
- **接口**: FastAPI + Streamlit + CLI

## 🚀 快速开始

### 环境要求

- Python 3.9+
- ripgrep (用于高效日志搜索)
- 自部署的DeepSeek-R1模型
- RAGFlow知识库服务

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd intelligent-log-analyzer
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置环境变量**
```bash
cp config.example.env .env
# 编辑 .env 文件，填入实际配置
```

4. **安装ripgrep**
```bash
# Ubuntu/Debian
sudo apt install ripgrep

# CentOS/RHEL
sudo yum install ripgrep

# macOS
brew install ripgrep
```

5. **初始化项目**
```bash
python scripts/setup.py
```

### 配置说明

编辑 `.env` 文件，配置以下关键参数：

```env
# DeepSeek模型配置
DEEPSEEK_API_BASE=http://your-deepseek-endpoint/v1
DEEPSEEK_API_KEY=your-api-key
DEEPSEEK_MODEL=deepseek/deepseek-r1

# RAGFlow知识库配置  
RAGFLOW_BASE_URL=http://your-ragflow-server
RAGFLOW_API_KEY=your-ragflow-key
RAGFLOW_CONVERSATION_ID=your-conversation-id
```

## 📖 使用指南

### 1. CLI使用

```bash
# 分析特定问题
python -m src.cli analyze \
  --problem "OAuth2 token AMADmZzmzAIor6ly31为什么会失效" \
  --log-dir "/path/to/oauth2-logs"

# 解压缩日志文件
python scripts/decompress.py /path/to/log-directory

# 构建搜索索引
python scripts/build_index.py /path/to/log-directory
```

### 2. API使用

启动API服务器：
```bash
uvicorn src.api.main:app --host 0.0.0.0 --port 8000
```

发送分析请求：
```bash
curl -X POST "http://localhost:8000/analyze" \
  -H "Content-Type: application/json" \
  -d '{
    "problem": "OAuth2 token失效问题分析",
    "log_directory": "/path/to/logs"
  }'
```

### 3. Web界面使用

```bash
streamlit run src/web/app.py
```

访问 http://localhost:8501 使用Web界面。

## 🔧 配置指南

### 核心配置项

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| `DEEPSEEK_TEMPERATURE` | 模型生成温度 | 0.6 |
| `SEARCH_MAX_RESULTS` | 最大搜索结果数 | 100 |
| `MAX_CONCURRENT_ANALYSES` | 最大并发分析数 | 5 |
| `ANALYSIS_TIMEOUT_MINUTES` | 分析超时时间 | 30 |

### 性能优化

- **内存限制**: 建议设置 `MEMORY_LIMIT_GB=4.0`
- **并行搜索**: 根据CPU核心数设置 `PARALLEL_SEARCHES`
- **缓存配置**: 启用 `SEARCH_CACHE_ENABLED=true`

## 📊 分析示例

### OAuth2 Token失效分析

**输入**:
```
问题: "OAuth2 token AMADmZzmzAIor6ly31为什么会失效?"
日志目录: "/data/oauth2-logs"
```

**输出**:
```markdown
# 日志分析报告

## 问题描述
OAuth2 token AMADmZzmzAIor6ly31失效原因分析

## 分析摘要
检测到并发请求导致的token覆盖问题

## 根本原因
Token在2025-07-10 20:00:00.109创建后，在41ms内被并发请求覆盖

## 关键证据
- 文件: pub-oauth2-provider-prod-86dfdf9655-pkws4_2025-07-10_1.log
- Redis Key: auth:token:app:195475686612496384
- 原Token: AMADmZzmzAIor6ly31 (被覆盖)
- 新Token: AMADnEwpzAIpqxvct9 (覆盖者)

## 建议措施
1. 检查客户端重复登录逻辑
2. 优化token刷新机制，避免并发冲突
3. 考虑增加token防重复覆盖机制
```

## 🧪 测试

运行单元测试：
```bash
pytest tests/unit/ -v
```

运行集成测试：
```bash
pytest tests/integration/ -v
```

测试覆盖率：
```bash
pytest --cov=src tests/
```

## 📈 监控和日志

### 应用日志

日志文件位置：`logs/app.log`

日志级别配置：
```env
LOG_LEVEL=INFO
LOG_FILE_ENABLED=true
```

### 性能监控

访问 `http://localhost:8000/metrics` 查看Prometheus指标。

## 🛠️ 开发指南

### 项目结构

```
intelligent-log-analyzer/
├── src/
│   ├── core/           # 核心模块
│   ├── tools/          # FunctionTool工具集
│   ├── agents/         # Agent实现
│   ├── utils/          # 工具类
│   └── config/         # 配置管理
├── tests/              # 测试
├── data/               # 数据目录
├── scripts/            # 脚本工具
└── docs/               # 文档
```

### 添加新的分析方法

1. 在RAGFlow知识库中添加新的分析文档
2. 更新关键词库和搜索策略
3. 在`src/tools/`中添加相应的工具函数
4. 编写单元测试

### 代码规范

使用pre-commit钩子：
```bash
pip install pre-commit
pre-commit install
```

代码格式化：
```bash
black src/
isort src/
flake8 src/
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📝 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🔗 相关链接

- [Google ADK文档](https://google.github.io/adk-docs/)
- [DeepSeek模型文档](https://api-docs.deepseek.com/)
- [RAGFlow项目](https://github.com/infiniflow/ragflow)
- [ripgrep工具](https://github.com/BurntSushi/ripgrep)

## ❓ 常见问题

### Q: DeepSeek API连接失败？
A: 检查 `DEEPSEEK_API_BASE` 和 `DEEPSEEK_API_KEY` 配置，确保网络连通性。

### Q: 搜索速度慢？
A: 确保已安装ripgrep，并考虑构建索引：`python scripts/build_index.py`

### Q: 内存不足？
A: 调整 `MEMORY_LIMIT_GB` 和 `MAX_CONCURRENT_ANALYSES` 参数。

### Q: 知识库查询失败？
A: 验证RAGFlow服务状态和API配置。

---

**技术支持**: 基于已验证的DeepSeek+ADK兼容性构建，确保企业级可靠性。 