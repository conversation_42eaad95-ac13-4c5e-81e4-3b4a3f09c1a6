# 智能日志分析器 PRD (Product Requirements Document)

## 项目概述

### 项目背景
基于新能源汽车后台系统的日志分析需求，开发一个通用的智能日志分析器，利用自部署的DeepSeek-R1模型结合Google ADK框架，实现大规模日志的智能分析和问题诊断。

### 核心价值
1. **解决大规模日志分析难题**: 618MB+ 的生产日志快速定位和分析
2. **提升运维效率**: 从人工排查到AI智能诊断，效率提升90%+
3. **知识沉淀**: 通过RAGFlow知识库积累分析经验，形成企业级知识资产
4. **通用架构**: 支持OAuth2、BMS、充电管理等多个后台模块

### 技术基础
- **LLM**: 自部署DeepSeek-R1模型 (已验证ADK兼容性)
- **框架**: Google ADK Python (FunctionTool模式100%可用)
- **知识库**: RAGFlow (已验证API集成)
- **部署**: SGLang加速部署，支持OpenAI API兼容

## 核心功能需求

### 1. 智能问题诊断
**功能描述**: 用户输入问题描述，系统自动分析相关日志并给出诊断结果

**输入示例**:
```
"OAuth2 token AMADmZzmzAIor6ly31为什么会失效?"
"Redis连接异常，影响了哪些业务模块?"
"2025-07-10到2025-07-15期间的系统性能问题分析"
```

**输出示例**:
```
分析报告：
问题分类: OAuth2 Token失效
根本原因: 并发请求导致token覆盖
影响时间: 2025-07-10 20:00:00.109 - 20:00:00.150 (41ms)
关键证据: 
- 日志文件: pub-oauth2-provider-prod-86dfdf9655-pkws4_2025-07-10_1.log
- Redis Key: auth:token:app:195475686612496384
- 原Token: AMADmZzmzAIor6ly31 (被覆盖)
- 新Token: AMADnEwpzAIpqxvct9 (覆盖者)
建议措施: 
1. 检查客户端重复登录逻辑
2. 优化token刷新机制
3. 增加防重复覆盖机制
```

**技术实现**:
- RAGFlow查询分析方法
- FunctionTool执行日志搜索
- 多轮LLM对话进行证据收集
- 智能报告生成

### 2. 大规模日志处理
**功能描述**: 自动处理GB级压缩日志文件，支持智能搜索和分段读取

**技术要求**:
- 支持.gz文件自动解压
- 支持81个文件并行处理
- 智能关键词搜索 (ripgrep)
- 分段读取适配DeepSeek上下文窗口

**性能指标**:
- 单个关键词搜索 < 5秒
- 文件定位准确率 > 95%
- 内存使用 < 2GB

### 3. 知识库驱动分析
**功能描述**: 基于RAGFlow知识库获取分析方法，支持经验积累和方法复用

**知识库内容**:
- OAuth2问题分析方法
- Redis连接问题诊断
- BMS系统异常排查
- 充电桩通信故障分析
- 通用日志模式识别

**智能决策流程**:
1. 用户输入问题 → RAGFlow查询分析方法
2. 有方法 → 按方法执行分析
3. 无方法 → LLM推理生成分析策略
4. 执行分析 → 结果验证 → 方法沉淀

### 4. 多轮对话分析
**功能描述**: 支持复杂问题的多轮深入分析，智能决策何时停止搜索

**对话流程**:
```
用户: "分析OAuth2 token失效问题"
系统: "正在搜索相关日志..."
系统: "发现token在多个文件中出现，正在分析时间线..."
系统: "检测到并发请求模式，需要进一步确认具体时间点吗?"
用户: "是的，给出详细时间线"
系统: "分析完成，生成详细报告"
```

**智能决策机制**:
- 证据充分度评估
- 搜索范围动态调整
- 分析深度自适应控制

## 技术架构设计

### 系统架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Interface │    │  CLI Interface  │    │   API Gateway   │
│   (Streamlit)   │    │   (Argparse)    │    │   (FastAPI)     │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
        ┌─────────────────────────▼─────────────────────────┐
        │            LogAnalysisAgent (ADK)                │
        │  - DeepSeek-R1 LLM                              │
        │  - Session Management                           │
        │  - Multi-turn Dialogue                          │
        └─────────────────────┬───────────────────────────┘
                              │
          ┌───────────────────┼───────────────────┐
          │                   │                   │
    ┌─────▼──────┐    ┌──────▼──────┐    ┌──────▼──────┐
    │ RAGFlow    │    │ FunctionTool│    │  Log Engine │
    │ Knowledge  │    │   Toolkit   │    │   (ripgrep) │
    │    Base    │    │             │    │             │
    └────────────┘    └─────────────┘    └─────────────┘
```

### 核心组件设计

#### 1. LogAnalysisAgent (主控制器)
```python
class LogAnalysisAgent:
    """基于ADK的智能日志分析Agent"""
    
    def __init__(self):
        self.llm = DeepSeekCompatibleLiteLLM()
        self.knowledge_base = RAGFlowClient()
        self.log_engine = LogSearchEngine()
        self.session_manager = SessionManager()
        
    async def analyze_problem(self, problem: str) -> AnalysisReport:
        """主分析流程"""
        
    async def multi_turn_analysis(self, context: AnalysisContext) -> bool:
        """多轮对话分析控制"""
```

#### 2. FunctionTool工具集
```python
# 核心工具函数
@FunctionTool
def ragflow_get_analysis_method(problem: str) -> AnalysisMethod

@FunctionTool  
def decompress_log_files(log_dir: str) -> DecompressionResult

@FunctionTool
def search_logs_by_keyword(keywords: List[str]) -> SearchResult

@FunctionTool
def smart_log_reader(file_path: str, line_range: tuple) -> LogContent

@FunctionTool
def update_analysis_state(findings: List[Finding]) -> AnalysisState
```

#### 3. LogSearchEngine (搜索引擎)
```python
class LogSearchEngine:
    """高性能日志搜索引擎"""
    
    def __init__(self):
        self.indexer = LogIndexer()  # 文件索引
        self.searcher = RipgrepSearcher()  # ripgrep封装
        
    def search(self, query: SearchQuery) -> SearchResult:
        """多策略搜索"""
        
    def build_index(self, log_directory: str) -> LogIndex:
        """建立日志索引"""
```

#### 4. RAGFlowClient (知识库客户端)
```python
class RAGFlowClient:
    """RAGFlow知识库集成客户端"""
    
    async def query_analysis_method(self, problem: str) -> AnalysisMethod:
        """查询分析方法"""
        
    async def save_analysis_case(self, case: AnalysisCase) -> bool:
        """保存分析案例"""
```

### 数据模型设计

#### 核心数据结构
```python
@dataclass
class AnalysisContext:
    """分析上下文"""
    problem_description: str
    current_step: str
    search_results: List[SearchResult]
    findings: List[Finding]
    analysis_state: AnalysisState
    
@dataclass  
class SearchResult:
    """搜索结果"""
    file_path: str
    line_number: int
    matched_content: str
    confidence_score: float
    timestamp: datetime
    
@dataclass
class Finding:
    """分析发现"""
    type: FindingType
    description: str
    evidence: List[Evidence]
    confidence: float
    
@dataclass
class AnalysisReport:
    """分析报告"""
    problem: str
    summary: str
    root_cause: str
    timeline: List[TimelineEvent]
    evidence: List[Evidence]
    recommendations: List[str]
    confidence_score: float
```

## 实现roadmap

### Phase 1: 基础框架 (Week 1-2)
- [x] DeepSeek+ADK兼容性验证 (已完成)
- [ ] 项目结构搭建
- [ ] 核心Agent框架
- [ ] FunctionTool工具集
- [ ] 基础单元测试

**交付物**: 
- 可运行的基础框架
- OAuth2 token分析demo
- 单元测试覆盖率>80%

### Phase 2: 搜索引擎 (Week 3-4)  
- [ ] LogSearchEngine实现
- [ ] ripgrep集成优化
- [ ] 文件索引系统
- [ ] 智能分段读取
- [ ] 性能基准测试

**交付物**:
- 高性能日志搜索引擎
- 支持GB级日志处理
- 搜索响应时间<5秒

### Phase 3: 知识库集成 (Week 5)
- [ ] RAGFlow深度集成
- [ ] 分析方法库建设
- [ ] 智能方法推荐
- [ ] 案例沉淀机制

**交付物**:
- 完整知识库系统
- 覆盖5个主要问题类型
- 智能方法推荐准确率>85%

### Phase 4: 多轮对话 (Week 6-7)
- [ ] 对话状态管理
- [ ] 智能决策引擎
- [ ] 分析深度控制
- [ ] 用户交互优化

**交付物**:
- 智能多轮对话系统
- 分析决策准确率>90%
- 用户体验优化

### Phase 5: 用户界面 (Week 8)
- [ ] Web界面 (Streamlit)
- [ ] CLI工具
- [ ] API接口
- [ ] 报告生成系统

**交付物**:
- 完整用户界面
- API文档
- 部署指南

### Phase 6: 生产部署 (Week 9-10)
- [ ] 性能优化
- [ ] 错误处理
- [ ] 监控系统
- [ ] 生产部署

**交付物**:
- 生产就绪系统
- 监控大盘
- 运维手册

## 成功评估指标

### 技术指标
- **响应时间**: 单次分析<60秒
- **准确率**: 问题诊断准确率>85%  
- **覆盖率**: 支持5+后台模块问题分析
- **性能**: 支持GB级日志处理

### 业务指标  
- **效率提升**: 日志分析效率提升90%+
- **知识沉淀**: 积累100+分析案例
- **用户满意度**: 运维团队满意度>4.5/5
- **成本节约**: 人工分析成本降低80%+

## 风险控制

### 技术风险
1. **DeepSeek API稳定性** 
   - 风险: 内部服务不稳定
   - 缓解: 官方API备用方案

2. **大文件处理性能**
   - 风险: 内存溢出、处理缓慢  
   - 缓解: 分片处理、智能索引

3. **LLM上下文限制**
   - 风险: 单次分析信息不足
   - 缓解: 多轮对话、状态管理

### 业务风险
1. **分析准确性不足**
   - 风险: 误诊导致业务影响
   - 缓解: 置信度评估、人工确认

2. **知识库内容不足**
   - 风险: 新问题类型无法处理
   - 缓解: 持续学习、专家标注

## 总结

本项目基于已验证的DeepSeek+ADK技术架构，通过FunctionTool模式实现可靠的大规模日志分析能力。核心创新点包括：

1. **首个DeepSeek+ADK企业级应用**: 开创性的技术组合
2. **知识库驱动的分析方法**: 经验积累和智能推荐  
3. **多轮对话的深度分析**: 复杂问题的智能拆解
4. **通用化的架构设计**: 支持多种后台模块扩展

**项目成功概率**: 85%+ (基于技术验证结果)
**预期ROI**: 人工成本节约80%+，分析效率提升90%+

---

*本PRD基于DeepSeek与ADK兼容性验证结果制定，技术方案已经过实际测试验证。* 