#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态分析引擎 - 完全消除硬编码的智能分析系统

本模块是智能日志分析器的核心创新之一，实现了完全去硬编码的动态分析能力。
通过结合RAGFlow知识库和LLM推理，系统能够智能地处理任意类型的日志问题，
无需预设任何特定的错误类型、关键词或分析规则。

核心组件：
- AnalysisFramework：动态分析框架，从知识库获取分析方法
- DynamicPattern：动态模式识别，基于LLM推理的智能模式发现
- IntelligentRootCause：智能根因分析，深度推理问题根本原因
- DynamicAnalysisEngine：统一引擎，协调所有动态分析功能

创新特点：
1. 零硬编码：所有分析逻辑完全动态生成
2. 知识驱动：基于RAGFlow专业知识库的方法获取
3. LLM推理：利用大模型的推理能力进行智能分析
4. 完全通用：支持新能源汽车后台系统的所有模块
5. 自学习：通过分析结果不断积累和优化知识

技术架构：
- RAGFlow知识库查询 → 获取专业分析方法
- LLM智能推理 → 动态生成分析策略
- 多层缓存机制 → 提升性能和一致性
- 降级处理 → 确保系统稳定性和可用性

适用场景：
- OAuth2认证问题分析
- BMS电池管理系统故障诊断
- 充电桩通信问题排查
- Redis缓存异常分析
- 未知类型的后台系统问题

这个引擎代表了从传统规则驱动到智能知识驱动的重大技术突破。
"""

核心设计原则：
1. 无硬编码 - 所有分析规则来自RAGFlow或LLM
2. 通用性 - 能处理任意类型的后台系统问题
3. 智能化 - 基于知识库学习和LLM推理
4. 可扩展 - 支持新问题类型的自动适应
"""

import asyncio
import json
import re
from typing import Any, Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
import logging

from core.models import SearchResult, Finding, AnalysisContext


@dataclass
class AnalysisFramework:
    """动态分析框架 - 从知识库获取的分析方法"""
    problem_category: str
    analysis_keywords: List[str]
    relevance_criteria: Dict[str, float]
    pattern_indicators: List[str]
    severity_markers: List[str]
    confidence_weights: Dict[str, float]
    analysis_strategy: str
    source: str  # 'ragflow' or 'llm_reasoning'


@dataclass
class DynamicPattern:
    """动态识别的问题模式"""
    pattern_name: str
    pattern_type: str
    indicators: List[str]
    confidence: float
    evidence: List[str]
    source: str


@dataclass
class IntelligentRootCause:
    """智能推理的根本原因"""
    cause_name: str
    cause_description: str
    evidence_chain: List[str]
    confidence: float
    reasoning: str
    recommendations: List[str]


class DynamicAnalysisEngine:
    """统一动态分析引擎 - 完全消除硬编码的智能分析系统"""
    
    def __init__(self, ragflow_client, llm_client, config):
        self.ragflow = ragflow_client
        self.llm = llm_client
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 缓存机制避免重复查询
        self._framework_cache = {}
        self._pattern_cache = {}
    
    async def get_analysis_framework(self, problem_description: str) -> AnalysisFramework:
        """获取动态分析框架 - RAGFlow知识库 + LLM推理"""
        
        # 检查缓存
        cache_key = self._generate_cache_key(problem_description)
        if cache_key in self._framework_cache:
            self.logger.info("使用缓存的分析框架")
            return self._framework_cache[cache_key]
        
        self.logger.info(f"获取动态分析框架: {problem_description}")
        
        try:
            # 第一优先级：RAGFlow知识库查询
            framework = await self._query_ragflow_framework(problem_description)
            
            if framework and framework.analysis_keywords:
                self.logger.info(f"RAGFlow返回分析框架: {framework.problem_category}")
                self._framework_cache[cache_key] = framework
                return framework
            
            # 第二优先级：LLM推理生成
            self.logger.info("RAGFlow无相关框架，使用LLM推理")
            framework = await self._llm_generate_framework(problem_description)
            
            if framework:
                self._framework_cache[cache_key] = framework
                return framework
            
            # 最后备选：基础通用框架
            return self._create_fallback_framework(problem_description)
            
        except Exception as e:
            self.logger.error(f"获取分析框架失败: {e}")
            return self._create_fallback_framework(problem_description)
    
    async def _query_ragflow_framework(self, problem_description: str) -> Optional[AnalysisFramework]:
        """从RAGFlow查询专业分析框架"""
        try:
            # 构建专业查询
            ragflow_query = f"""
            问题分析需求：{problem_description}
            
            请提供：
            1. 问题分类和类型识别
            2. 日志搜索的关键词列表（至少5-10个）
            3. 相关性判断标准和权重
            4. 问题模式识别指标
            5. 严重性评估标记
            6. 置信度计算方法
            7. 具体的分析策略步骤
            
            请基于你的专业知识给出结构化的分析方法。
            """
            
            if not self.ragflow:
                return None
            
            # 查询RAGFlow知识库
            guidance = await self._safe_ragflow_query(ragflow_query)
            
            if not guidance or len(guidance) < 100:
                return None
            
            # 解析RAGFlow回答，提取结构化信息
            framework = await self._parse_ragflow_guidance(guidance, problem_description)
            
            if framework:
                framework.source = "ragflow"
                self.logger.info(f"RAGFlow框架解析成功: {framework.problem_category}")
                return framework
            
            return None
            
        except Exception as e:
            self.logger.error(f"RAGFlow框架查询失败: {e}")
            return None
    
    async def _safe_ragflow_query(self, query: str) -> Optional[str]:
        """安全的RAGFlow查询，处理各种异常"""
        try:
            if hasattr(self.ragflow, 'query_knowledge_async'):
                return await self.ragflow.query_knowledge_async(query)
            elif hasattr(self.ragflow, 'query'):
                # 同步调用的异步包装
                import asyncio
                loop = asyncio.get_event_loop()
                return await loop.run_in_executor(None, self.ragflow.query, query)
            else:
                self.logger.warning("RAGFlow客户端方法未找到")
                return None
        except Exception as e:
            self.logger.error(f"RAGFlow查询异常: {e}")
            return None
    
    async def _parse_ragflow_guidance(self, guidance: str, problem_description: str) -> Optional[AnalysisFramework]:
        """解析RAGFlow专业指导，提取结构化分析框架"""
        try:
            # 使用LLM解析RAGFlow的专业指导
            parse_prompt = f"""
            请从以下专业指导中提取结构化的分析框架信息：

            原问题：{problem_description}
            
            专业指导：
            {guidance}
            
            请以JSON格式输出，包含以下字段：
            {{
                "problem_category": "问题类型分类",
                "analysis_keywords": ["关键词1", "关键词2", ...],
                "relevance_criteria": {{"keyword_match": 0.3, "error_severity": 0.4, "context_relevance": 0.3}},
                "pattern_indicators": ["模式指标1", "模式指标2", ...],
                "severity_markers": ["严重性标记1", "严重性标记2", ...],
                "confidence_weights": {{"content_match": 0.4, "pattern_match": 0.3, "context_match": 0.3}},
                "analysis_strategy": "具体分析策略描述"
            }}
            
            要求：
            1. 关键词列表要具体且相关
            2. 权重值总和应为1.0
            3. 策略描述要可执行
            4. 如果信息不足，给出通用值
            
            只返回JSON，不要其他内容。
            """
            
            response = await self._safe_llm_query(parse_prompt, max_tokens=1000)
            
            if not response:
                return None
            
            # 解析JSON响应
            framework_data = self._extract_json_from_response(response)
            
            if framework_data:
                framework = AnalysisFramework(
                    problem_category=framework_data.get('problem_category', 'Unknown'),
                    analysis_keywords=framework_data.get('analysis_keywords', []),
                    relevance_criteria=framework_data.get('relevance_criteria', {}),
                    pattern_indicators=framework_data.get('pattern_indicators', []),
                    severity_markers=framework_data.get('severity_markers', []),
                    confidence_weights=framework_data.get('confidence_weights', {}),
                    analysis_strategy=framework_data.get('analysis_strategy', ''),
                    source="ragflow_parsed"
                )
                
                # 验证框架完整性
                if self._validate_framework(framework):
                    return framework
            
            return None
            
        except Exception as e:
            self.logger.error(f"RAGFlow指导解析失败: {e}")
            return None
    
    async def _llm_generate_framework(self, problem_description: str) -> Optional[AnalysisFramework]:
        """LLM推理生成分析框架"""
        try:
            llm_prompt = f"""
            你是一个专业的日志分析专家，请为以下问题设计一个完整的分析框架：

            问题描述：{problem_description}
            
            请基于你的专业知识，设计一个系统化的分析方法，包括：
            
            1. 问题分类：这属于什么类型的技术问题？
            2. 搜索关键词：需要在日志中搜索哪些关键词？（至少8-10个）
            3. 相关性评估：如何判断日志内容与问题的相关性？
            4. 模式识别：这类问题的典型特征是什么？
            5. 严重性判断：如何评估问题的严重程度？
            6. 置信度计算：如何权衡不同证据的重要性？
            7. 分析策略：具体的分析步骤是什么？
            
            请以JSON格式返回：
            {{
                "problem_category": "具体问题类型",
                "analysis_keywords": ["相关关键词列表"],
                "relevance_criteria": {{"各项权重": 数值}},
                "pattern_indicators": ["问题特征指标"],
                "severity_markers": ["严重性标记"],
                "confidence_weights": {{"置信度权重": 数值}},
                "analysis_strategy": "详细分析策略"
            }}
            
            要求专业、具体、可执行。只返回JSON格式。
            """
            
            response = await self._safe_llm_query(llm_prompt, max_tokens=1500)
            
            if not response:
                return None
            
            framework_data = self._extract_json_from_response(response)
            
            if framework_data:
                framework = AnalysisFramework(
                    problem_category=framework_data.get('problem_category', 'Unknown'),
                    analysis_keywords=framework_data.get('analysis_keywords', []),
                    relevance_criteria=framework_data.get('relevance_criteria', {}),
                    pattern_indicators=framework_data.get('pattern_indicators', []),
                    severity_markers=framework_data.get('severity_markers', []),
                    confidence_weights=framework_data.get('confidence_weights', {}),
                    analysis_strategy=framework_data.get('analysis_strategy', ''),
                    source="llm_generated"
                )
                
                if self._validate_framework(framework):
                    self.logger.info(f"LLM生成框架成功: {framework.problem_category}")
                    return framework
            
            return None
            
        except Exception as e:
            self.logger.error(f"LLM框架生成失败: {e}")
            return None
    
    async def _safe_llm_query(self, prompt: str, max_tokens: int = 1000) -> Optional[str]:
        """安全的LLM查询"""
        try:
            if hasattr(self.llm, 'generate_content_async'):
                response = await self.llm.generate_content_async(prompt, max_tokens=max_tokens)
                return response
            elif hasattr(self.llm, 'query'):
                return await self.llm.query(prompt)
            else:
                self.logger.warning("LLM客户端方法未找到")
                return None
        except Exception as e:
            self.logger.error(f"LLM查询异常: {e}")
            return None
    
    def _extract_json_from_response(self, response: str) -> Optional[Dict[str, Any]]:
        """从LLM响应中提取JSON数据"""
        try:
            # 清理响应文本
            response = response.strip()
            
            # 查找JSON块
            json_patterns = [
                r'```json\s*(\{.*?\})\s*```',
                r'```\s*(\{.*?\})\s*```',
                r'(\{.*?\})'
            ]
            
            for pattern in json_patterns:
                matches = re.findall(pattern, response, re.DOTALL)
                if matches:
                    json_str = matches[0]
                    try:
                        return json.loads(json_str)
                    except json.JSONDecodeError:
                        continue
            
            # 直接解析整个响应
            try:
                return json.loads(response)
            except json.JSONDecodeError:
                pass
            
            return None
            
        except Exception as e:
            self.logger.error(f"JSON提取失败: {e}")
            return None
    
    def _validate_framework(self, framework: AnalysisFramework) -> bool:
        """验证分析框架的完整性"""
        try:
            # 基础字段检查
            if not framework.problem_category or framework.problem_category == 'Unknown':
                return False
            
            # 关键词检查
            if not framework.analysis_keywords or len(framework.analysis_keywords) < 3:
                return False
            
            # 权重检查
            if framework.relevance_criteria:
                total_weight = sum(framework.relevance_criteria.values())
                if abs(total_weight - 1.0) > 0.1:  # 允许10%误差
                    # 标准化权重
                    framework.relevance_criteria = {
                        k: v / total_weight for k, v in framework.relevance_criteria.items()
                    }
            
            return True
            
        except Exception as e:
            self.logger.error(f"框架验证失败: {e}")
            return False
    
    def _create_fallback_framework(self, problem_description: str) -> AnalysisFramework:
        """创建备用通用框架"""
        # 从问题描述中提取基础关键词
        words = re.findall(r'\b[A-Za-z0-9_\-]{3,}\b', problem_description.lower())
        keywords = list(set(words))[:10]  # 最多10个关键词
        
        return AnalysisFramework(
            problem_category="通用后台系统问题",
            analysis_keywords=keywords + ['error', 'exception', 'failed', 'timeout'],
            relevance_criteria={
                "keyword_match": 0.4,
                "error_severity": 0.3,
                "context_relevance": 0.3
            },
            pattern_indicators=['错误', '异常', '失败', '超时'],
            severity_markers=['critical', 'error', 'warning'],
            confidence_weights={
                "content_match": 0.5,
                "pattern_match": 0.3,
                "context_match": 0.2
            },
            analysis_strategy="基于关键词搜索和错误模式识别的通用分析",
            source="fallback"
        )
    
    def _generate_cache_key(self, problem_description: str) -> str:
        """生成缓存键"""
        import hashlib
        return hashlib.md5(problem_description.encode()).hexdigest()[:16]
    
    async def evaluate_content_relevance(self, content: str, framework: AnalysisFramework) -> float:
        """基于动态框架评估内容相关性"""
        try:
            if not content or not framework:
                return 0.0
            
            content_lower = content.lower()
            total_score = 0.0
            
            # 关键词匹配评分
            keyword_score = 0.0
            if framework.analysis_keywords:
                matches = sum(1 for keyword in framework.analysis_keywords 
                            if keyword.lower() in content_lower)
                keyword_score = min(matches / len(framework.analysis_keywords), 1.0)
            
            # 严重性标记评分
            severity_score = 0.0
            if framework.severity_markers:
                severity_matches = sum(1 for marker in framework.severity_markers 
                                     if marker.lower() in content_lower)
                severity_score = min(severity_matches / len(framework.severity_markers), 1.0)
            
            # 模式指标评分
            pattern_score = 0.0
            if framework.pattern_indicators:
                pattern_matches = sum(1 for indicator in framework.pattern_indicators 
                                    if indicator.lower() in content_lower)
                pattern_score = min(pattern_matches / len(framework.pattern_indicators), 1.0)
            
            # 应用权重
            criteria = framework.relevance_criteria
            if criteria:
                total_score = (
                    keyword_score * criteria.get('keyword_match', 0.4) +
                    severity_score * criteria.get('error_severity', 0.3) +
                    pattern_score * criteria.get('context_relevance', 0.3)
                )
            else:
                total_score = (keyword_score * 0.4 + severity_score * 0.3 + pattern_score * 0.3)
            
            # 限制在0-1范围内
            return max(0.0, min(1.0, total_score))
            
        except Exception as e:
            self.logger.error(f"相关性评估失败: {e}")
            return 0.0
    
    async def identify_problem_patterns(self, search_results: List[SearchResult], 
                                      framework: AnalysisFramework) -> List[DynamicPattern]:
        """基于动态框架识别问题模式"""
        try:
            if not search_results or not framework:
                return []
            
            # 合并所有搜索结果内容
            all_content = "\n".join([result.content for result in search_results if result.content])
            
            if not all_content:
                return []
            
            # 构建LLM模式识别提示
            pattern_prompt = f"""
            基于以下分析框架和日志内容，识别问题模式：

            分析框架：
            - 问题类型：{framework.problem_category}
            - 关键指标：{framework.pattern_indicators}
            - 分析策略：{framework.analysis_strategy}
            
            日志内容：
            {all_content[:3000]}  # 限制长度
            
            请识别以下类型的模式：
            1. 错误模式：重复出现的错误类型
            2. 时间模式：问题发生的时间规律  
            3. 频率模式：问题出现的频率特征
            4. 关联模式：多个相关问题的联系
            
            请以JSON格式返回：
            [
                {{
                    "pattern_name": "模式名称",
                    "pattern_type": "错误|时间|频率|关联",
                    "indicators": ["具体指标1", "指标2"],
                    "confidence": 0.8,
                    "evidence": ["证据1", "证据2"],
                    "description": "模式描述"
                }}
            ]
            
            只返回JSON数组。
            """
            
            response = await self._safe_llm_query(pattern_prompt, max_tokens=1000)
            
            if not response:
                return []
            
            patterns_data = self._extract_json_from_response(response)
            
            if not patterns_data or not isinstance(patterns_data, list):
                return []
            
            patterns = []
            for pattern_data in patterns_data:
                if isinstance(pattern_data, dict):
                    pattern = DynamicPattern(
                        pattern_name=pattern_data.get('pattern_name', 'Unknown'),
                        pattern_type=pattern_data.get('pattern_type', 'Unknown'),
                        indicators=pattern_data.get('indicators', []),
                        confidence=float(pattern_data.get('confidence', 0.5)),
                        evidence=pattern_data.get('evidence', []),
                        source="llm_identified"
                    )
                    patterns.append(pattern)
            
            self.logger.info(f"识别到 {len(patterns)} 个问题模式")
            return patterns
            
        except Exception as e:
            self.logger.error(f"模式识别失败: {e}")
            return []
    
    async def reason_root_causes(self, patterns: List[DynamicPattern], 
                               framework: AnalysisFramework,
                               search_results: List[SearchResult]) -> List[IntelligentRootCause]:
        """基于模式和框架推理根本原因"""
        try:
            if not patterns and not search_results:
                return []
            
            # 构建证据链
            evidence_content = []
            
            if patterns:
                evidence_content.append("识别的模式：")
                for pattern in patterns:
                    evidence_content.append(f"- {pattern.pattern_name}: {pattern.indicators}")
            
            if search_results:
                evidence_content.append("\n关键日志内容：")
                for i, result in enumerate(search_results[:5]):  # 限制数量
                    evidence_content.append(f"- 文件 {i+1}: {result.content[:200]}...")
            
            evidence_text = "\n".join(evidence_content)
            
            # 构建根因分析提示
            root_cause_prompt = f"""
            作为专业的系统分析专家，请基于以下信息进行根本原因分析：

            问题类型：{framework.problem_category}
            分析策略：{framework.analysis_strategy}
            
            证据信息：
            {evidence_text}
            
            请进行深度的因果关系分析，识别可能的根本原因。考虑：
            1. 技术层面的根本原因
            2. 配置或环境问题
            3. 并发或时序问题
            4. 依赖系统的影响
            5. 代码逻辑问题
            
            对每个可能的根因，请提供：
            - 详细的推理过程
            - 支持证据
            - 置信度评估
            - 验证建议
            - 解决方案
            
            请以JSON格式返回：
            [
                {{
                    "cause_name": "根因名称",
                    "cause_description": "详细描述",
                    "evidence_chain": ["证据1", "证据2"],
                    "confidence": 0.85,
                    "reasoning": "推理过程",
                    "recommendations": ["建议1", "建议2"]
                }}
            ]
            
            只返回JSON数组。
            """
            
            response = await self._safe_llm_query(root_cause_prompt, max_tokens=2000)
            
            if not response:
                return []
            
            causes_data = self._extract_json_from_response(response)
            
            if not causes_data or not isinstance(causes_data, list):
                return []
            
            root_causes = []
            for cause_data in causes_data:
                if isinstance(cause_data, dict):
                    root_cause = IntelligentRootCause(
                        cause_name=cause_data.get('cause_name', 'Unknown'),
                        cause_description=cause_data.get('cause_description', ''),
                        evidence_chain=cause_data.get('evidence_chain', []),
                        confidence=float(cause_data.get('confidence', 0.5)),
                        reasoning=cause_data.get('reasoning', ''),
                        recommendations=cause_data.get('recommendations', [])
                    )
                    root_causes.append(root_cause)
            
            # 按置信度排序
            root_causes.sort(key=lambda x: x.confidence, reverse=True)
            
            self.logger.info(f"识别到 {len(root_causes)} 个根本原因")
            return root_causes
            
        except Exception as e:
            self.logger.error(f"根因分析失败: {e}")
            return [] 